import logging
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from gentekai.agent import init_agent_system
from gentekai.api.endpoints.slack import slack_router
from gentekai.api.endpoints.webhooks import webhook_router
from gentekai.api.router import api_router
from gentekai.config import settings
from gentekai.db.session import close_db, init_db

# logfire.configure()
# logfire.instrument_pydantic_ai()
logging.basicConfig(level=logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    await init_db()
    await init_agent_system()
    logging.info("Application started")
    yield
    # Shutdown
    await close_db()
    logging.info("Application shutting down")


app = FastAPI(
    title="GenTekai Multi-Agent Platform",
    description="AI-powered multi-agent conversation platform",
    version="1.0.0",
    lifespan=lifespan,
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check
@app.get("/-/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}


# Routers
app.include_router(api_router, prefix="/api")
app.include_router(webhook_router, prefix="/webhook", tags=["webhook"])

app.include_router(slack_router, prefix="/slack", tags=["slack"])

import time

import httpx
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, Request
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt

from gentekai.config import settings

JWKS_CACHE = {"keys": None, "expires_at": 0}

CLERK_ISSUER = (
    settings.CLERK_ISSUER
)  # Example: https://clerk.<your-app>.clerk.accounts.dev
JWKS_URL = f"{CLERK_ISSUER}/.well-known/jwks.json"
AUDIENCE = settings.CLERK_PUBLISHABLE_KEY  # Get this from your Clerk dashboard


# -- JW<PERSON> fetching/caching --
async def get_jwks():
    now = time.time()
    if J<PERSON><PERSON>_CACHE["keys"] and J<PERSON><PERSON>_CACHE["expires_at"] > now:
        return JWKS_CACHE["keys"]
    async with httpx.AsyncClient() as client:
        resp = await client.get(JWKS_URL)
        resp.raise_for_status()
        JWKS_CACHE["keys"] = resp.json()["keys"]
        JW<PERSON>_CACHE["expires_at"] = now + 60 * 60  # cache 1 hour
        return JW<PERSON>_CACHE["keys"]


def get_key_for_kid(kid, jwks):
    for key in jwks:
        if key["kid"] == kid:
            return key
    return None


# -- Dependency for FastAPI --
async def verify_clerk_jwt(request: Request) -> dict:
    auth = request.headers.get("authorization")
    if not auth or not auth.lower().startswith("bearer "):
        raise HTTPException(status_code=401, detail="Missing Bearer token")
    token = auth[7:]

    # Decode token headers (to get kid)
    try:
        headers = jwt.get_unverified_header(token)
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid JWT header")
    kid = headers.get("kid")
    if not kid:
        raise HTTPException(status_code=401, detail="JWT missing 'kid'")

    jwks = await get_jwks()
    key = get_key_for_kid(kid, jwks)
    if not key:
        raise HTTPException(status_code=401, detail="Public key not found for 'kid'")

    try:
        # See Clerk docs for audience (aud) and issuer (iss) claims for your app
        payload = jwt.decode(
            token,
            key,
            algorithms=[key["alg"]],
            audience=AUDIENCE,
            issuer=CLERK_ISSUER,
            options={"verify_at_hash": False},  # Add other options as needed
        )
        # At this point, payload is a dict with user claims
        return payload
    except JWTError as e:
        raise HTTPException(status_code=401, detail=f"JWT validation failed: {e}")

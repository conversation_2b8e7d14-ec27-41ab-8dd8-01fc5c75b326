"""
Agent registry and capabilities definition
"""

from .models import Agent<PERSON>apa<PERSON>, AgentInfo

# Enhanced Agent Registry with capabilities
ENHANCED_AGENT_REGISTRY = {
    "employee_agent": AgentInfo(
        name="employee_agent",
        description="Handles personal employee data and profile management",
        capabilities=[
            AgentCapability(
                name="profile_update",
                description="Update user profile information",
                required_permissions=["read_profile", "write_profile"],
                complexity_level="simple",
            ),
            AgentCapability(
                name="personal_data_query",
                description="Retrieve personal employee information",
                required_permissions=["read_profile"],
                complexity_level="simple",
            ),
            AgentCapability(
                name="generic_employee_task",
                description="Create calendar event, meeting",
                complexity_level="simple",
            ),
        ],
        fallback_agents=["general_agent"],
        success_rate=0.95,
    ),
    "hr_agent": AgentInfo(
        name="hr_agent",
        description="Manages HR tasks, employee records, and organizational data",
        capabilities=[
            AgentCapability(
                name="employee_management",
                description="Manage employee records and HR processes",
                required_permissions=["hr_read", "hr_write"],
                complexity_level="moderate",
            ),
            AgentCapability(
                name="policy_queries",
                description="Answer questions about company policies",
                required_permissions=["policy_read"],
                complexity_level="simple",
            ),
            AgentCapability(
                name="document_extraction",
                description="Extract content from documents stored in R2 (PDF, Word, Excel, Images)",
                required_permissions=["document_read", "r2_access"],
                complexity_level="moderate",
            ),
        ],
        fallback_agents=["general_agent"],
        success_rate=0.90,
    ),
    "finance_agent": AgentInfo(
        name="finance_agent",
        description="Handles financial data, payroll, and expense management",
        capabilities=[
            AgentCapability(
                name="payroll_processing",
                description="Process payroll and salary information",
                required_permissions=["finance_read", "payroll_access"],
                complexity_level="complex",
            ),
            AgentCapability(
                name="expense_tracking",
                description="Track and manage expenses",
                required_permissions=["finance_read", "expense_write"],
                complexity_level="moderate",
            ),
        ],
        fallback_agents=["hr_agent", "general_agent"],
        success_rate=0.88,
    ),
    "general_agent": AgentInfo(
        name="general_agent",
        description="Handles general queries and fallback scenarios",
        capabilities=[
            AgentCapability(
                name="general_assistance",
                description="Provide general help and information",
                complexity_level="simple",
            )
        ],
        fallback_agents=[],
        success_rate=0.85,
    ),
}


def get_agent_info(agent_name: str) -> AgentInfo:
    """Get agent info by name"""
    return ENHANCED_AGENT_REGISTRY.get(agent_name)


def get_available_agents() -> list[str]:
    """Get list of available agent names"""
    return list(ENHANCED_AGENT_REGISTRY.keys())


def find_agents_by_capability(capability_name: str) -> list[str]:
    """Find agents that have a specific capability"""
    agents = []
    for agent_name, agent_info in ENHANCED_AGENT_REGISTRY.items():
        for capability in agent_info.capabilities:
            if capability.name == capability_name:
                agents.append(agent_name)
                break
    return agents


def find_agents_by_permission(required_permission: str) -> list[str]:
    """Find agents that have a specific permission"""
    agents = []
    for agent_name, agent_info in ENHANCED_AGENT_REGISTRY.items():
        for capability in agent_info.capabilities:
            if required_permission in capability.required_permissions:
                agents.append(agent_name)
                break
    return agents

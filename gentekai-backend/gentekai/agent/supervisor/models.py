"""
Pydantic models for the supervisor system
"""

from enum import Enum
from typing import Any, Dict, List, Literal

from pydantic import BaseModel, Field, field_validator


class TaskComplexity(str, Enum):
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    MULTI_AGENT = "multi_agent"


class ComplexityAnalysis(BaseModel):
    complexity: TaskComplexity
    reasoning: str
    estimated_steps: int
    requires_multiple_tools: bool


class AgentCapability(BaseModel):
    name: str
    description: str
    required_permissions: List[str] = []
    estimated_time: str = "< 1 min"
    complexity_level: Literal["simple", "moderate", "complex"] = "simple"


class AgentInfo(BaseModel):
    name: str
    description: str
    capabilities: List[AgentCapability]
    fallback_agents: List[str] = []
    max_concurrent_tasks: int = 1
    success_rate: float = 1.0


class SupervisorDecision(BaseModel):
    action: Literal["route", "chain", "delegate", "handle_directly", "escalate"] = (
        Field(..., description="Action to take")
    )
    agent_sequence: List[str] = Field(..., description="Sequence of agents to use")
    coordination_needed: bool = Field(
        default=False, description="Whether agents need to coordinate"
    )
    monitoring_level: Literal["none", "basic", "detailed"] = Field(
        default="basic", description="Level of monitoring needed"
    )
    success_criteria: str = Field(..., description="How to measure success")


class SupervisorDependencies(BaseModel):
    user_id: str
    user_role: str = "user"
    conversation_id: str
    chat_history: List[Dict[str, Any]] = []
    agent_registry: Dict[str, AgentInfo] = Field(default_factory=dict)

    # Agent performance tracking
    agent_performance: Dict[str, float] = Field(default_factory=dict)
    current_agent_loads: Dict[str, int] = Field(default_factory=dict)


class TaskCompletionResult(BaseModel):
    """For single-step tasks with self-evaluation"""

    user_response: str = Field(..., description="Natural response to give to the user")
    task_completed: bool = Field(
        ..., description="Whether the task was completed successfully"
    )
    confidence_level: Literal["low", "medium", "high"] = Field(
        ..., description="Agent's confidence in the result"
    )
    reasoning: str = Field(
        ..., description="Internal explanation of what was done and why"
    )
    potential_issues: List[str] = Field(
        default=[], description="Any potential issues or concerns"
    )

    @field_validator("reasoning")
    @classmethod
    def validate_reasoning(cls, v):
        if len(v) < 20:
            raise ValueError(
                "Reasoning must be at least 20 characters - provide more detailed explanation"
            )
        return v

    @field_validator("user_response")
    @classmethod
    def validate_user_response(cls, v):
        if len(v) < 10:
            raise ValueError(
                "User response too brief - provide a more helpful response"
            )
        return v


class MultiStepTask(BaseModel):
    """For complex tasks requiring step-by-step self-evaluation"""

    user_response: str = Field(..., description="Natural response to give to the user")
    steps_completed: List[str] = Field(..., description="List of completed steps")
    current_step: str = Field(..., description="Current step being worked on")
    next_steps: List[str] = Field(..., description="Remaining steps")
    step_quality_scores: Dict[str, int] = Field(
        ..., description="Quality score (1-10) for each completed step"
    )
    overall_progress: float = Field(
        ..., description="Overall progress percentage (0-100)"
    )
    blockers: List[str] = Field(
        default=[], description="Any blockers or issues encountered"
    )

    @field_validator("user_response")
    @classmethod
    def validate_user_response(cls, v):
        if len(v) < 10:
            raise ValueError(
                "User response too brief - provide a more helpful response"
            )
        return v

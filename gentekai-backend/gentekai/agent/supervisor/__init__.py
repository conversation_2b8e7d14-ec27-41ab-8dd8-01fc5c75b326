"""
Enhanced Supervisor System for Multi-Agent Coordination

This package provides a sophisticated supervisor system that can:
- Route simple tasks to appropriate agents
- Coordinate complex multi-agent workflows
- Monitor performance and handle failures
- Escalate risky tasks to human oversight

Usage:
    from app.agent.supervisor import initialize_supervisor_system, orchestrate_with_supervisor

    # Initialize once at startup
    agent_handlers = {
        "employee_agent": employee_agent_handle,
        "hr_agent": hr_agent_handle,
        "general_agent": general_agent_handle,
    }
    await initialize_supervisor_system(agent_handlers)

    # Use in your chat service
    async for chunk in orchestrate_with_supervisor(user, message, chat_history, model):
        yield chunk
"""

# Core functionality exports
from .core import (
    SUPERVISOR_CONFIG,
    configure_supervisor,
    get_supervisor_status,
    get_system_health,
    initialize_supervisor_system,
    orchestrate_with_supervisor,
    performance_monitor,
    record_agent_performance,
    record_supervisor_performance,
    supervisor_monitor,
)

# Model exports for type hints
from .models import (
    AgentCapability,
    AgentInfo,
    MultiStepTask,
    SupervisorDecision,
    TaskCompletionResult,
    TaskComplexity,
)

# Registry exports
from .registry import (
    ENHANCED_AGENT_REGISTRY,
    find_agents_by_capability,
    find_agents_by_permission,
    get_agent_info,
    get_available_agents,
)

__all__ = [
    # Core functions - main interface
    "orchestrate_with_supervisor",
    "initialize_supervisor_system",
    "get_supervisor_status",
    "get_system_health",
    # Performance tracking
    "record_supervisor_performance",
    "record_agent_performance",
    "performance_monitor",
    "supervisor_monitor",
    # Configuration
    "configure_supervisor",
    "SUPERVISOR_CONFIG",
    # Models for type hints
    "SupervisorDecision",
    "TaskComplexity",
    "AgentInfo",
    "AgentCapability",
    "TaskCompletionResult",
    "MultiStepTask",
    # Registry utilities
    "ENHANCED_AGENT_REGISTRY",
    "get_agent_info",
    "get_available_agents",
    "find_agents_by_capability",
    "find_agents_by_permission",
]

__version__ = "1.0.0"

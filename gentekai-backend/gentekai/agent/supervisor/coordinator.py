"""
Multi-agent workflow coordination
"""

import logging
from typing import Any, Dict, List

from .models import SupervisorDecision
from .router import <PERSON>hanced<PERSON>outer

logger = logging.getLogger(__name__)


class AgentCoordinator:
    """Coordinates multi-agent workflows"""

    def __init__(self, agent_handlers: Dict[str, Any]):
        self.agent_handlers = agent_handlers
        self.router = EnhancedRouter()
        self.active_tasks = {}

    async def execute_supervised_workflow(
        self, user, message: str, chat_history: List[Dict], model: str = None
    ):
        """Execute a workflow supervised by the supervisor agent"""

        # Get supervision decision
        try:
            decision = await self.router.route_with_supervision(
                user, message, chat_history
            )
        except Exception as e:
            logger.error(f"Supervisor routing failed: {e}")
            # Fallback to simple routing
            decision = SupervisorDecision(
                action="route",
                agent_sequence=["general_agent"],
                coordination_needed=False,
                monitoring_level="basic",
                success_criteria="Provide helpful response",
            )

        logger.info(f"Supervisor decision: {decision.action}")
        logger.info(f"Agent sequence: {decision.agent_sequence}")
        logger.info(f"Coordination needed: {decision.coordination_needed}")
        logger.info(f"Monitoring level: {decision.monitoring_level}")

        try:
            if decision.action == "route":
                # Simple routing to single agent
                async for chunk in self._execute_single_agent(
                    user, message, chat_history, decision, model
                ):
                    yield chunk

            elif decision.action == "chain":
                # Chain multiple agents in sequence
                async for chunk in self._execute_agent_chain(
                    user, message, chat_history, decision, model
                ):
                    yield chunk

            elif decision.action == "delegate":
                # Delegate with coordination
                async for chunk in self._execute_delegated_workflow(
                    user, message, chat_history, decision, model
                ):
                    yield chunk

            elif decision.action == "handle_directly":
                # Supervisor handles directly (complex coordination)
                async for chunk in self._execute_direct_supervision(
                    user, message, chat_history, decision, model
                ):
                    yield chunk

            elif decision.action == "escalate":
                # Escalate to human
                yield "⚠️ Task requires human oversight - escalating to support team...\n"
                yield f"📋 Reason: {decision.success_criteria}\n"
                yield "A human supervisor will review this request and respond shortly.\n"

            else:
                # Fallback
                logger.warning(f"Unknown supervisor action: {decision.action}")
                async for chunk in self._execute_single_agent(
                    user, message, chat_history, decision, model
                ):
                    yield chunk

        except Exception as e:
            logger.error(f"Supervised workflow execution failed: {e}")
            yield f"⚠️ Supervisor encountered an issue: {str(e)}\n"
            yield "Falling back to general assistance...\n\n"

            # Emergency fallback
            agent_handler = self.agent_handlers["general_agent"]
            async for chunk in agent_handler(user, message, chat_history, model):
                yield chunk

    async def _execute_single_agent(self, user, message, chat_history, decision, model):
        """Execute a single agent - FIXED to not show internal messages"""

        agent_name = (
            decision.agent_sequence[0] if decision.agent_sequence else "general_agent"
        )
        agent_handler = self.agent_handlers.get(
            agent_name, self.agent_handlers["general_agent"]
        )

        # Only show monitoring for detailed level AND complex tasks
        if decision.monitoring_level == "detailed" and len(decision.agent_sequence) > 1:
            logger.info(f"🔍 Coordinating with {agent_name}...")

        # Stream agent response directly without internal messages
        async for chunk in agent_handler(user, message, chat_history, model):
            yield chunk

    async def _execute_agent_chain(self, user, message, chat_history, decision, model):
        """Execute multiple agents in sequence"""

        logger.info(
            f"🔗 Supervisor coordinating {len(decision.agent_sequence)} agents..."
        )

        accumulated_context = []
        current_message = message

        for i, agent_name in enumerate(decision.agent_sequence):
            logger.info(
                f"Executing agent {i + 1}/{len(decision.agent_sequence)}: {agent_name}"
            )

            if i > 0:
                logger.info(
                    f"\n📊 Progress: {i}/{len(decision.agent_sequence)} stages complete\n"
                )
                logger.info(f"▶️ Stage {i + 1}: Engaging {agent_name}...\n\n")

            agent_handler = self.agent_handlers.get(
                agent_name, self.agent_handlers["general_agent"]
            )

            # Collect full response from agent
            agent_response = ""
            async for chunk in agent_handler(
                user, current_message, chat_history, model
            ):
                if chunk != "\n\ndata: [DONE]\n\n":
                    agent_response += chunk
                    yield chunk

            # Add to context for next agent
            accumulated_context.append(
                {"agent": agent_name, "response": agent_response.strip()}
            )

            # Update message for next agent in chain
            if i < len(decision.agent_sequence) - 1:
                current_message = f"Building on previous work: {agent_response.strip()}\n\nContinue with: {message}"
                logger.info(
                    f"✅ Stage {i + 1} complete. Supervisor coordinating next stage..."
                )

        logger.info(
            f"🎉 All {len(decision.agent_sequence)} stages completed successfully!"
        )

    async def _execute_delegated_workflow(
        self, user, message, chat_history, decision, model
    ):
        """Execute delegated workflow with coordination"""

        logger.info("🤝 Supervisor delegating to specialized teams...")

        # This could run agents in parallel or with specific coordination
        # For now, implementing as sequential with coordination messages
        for agent_name in decision.agent_sequence:
            logger.info(f"🤖 Delegating to {agent_name}")

            agent_handler = self.agent_handlers.get(
                agent_name, self.agent_handlers["general_agent"]
            )

            async for chunk in agent_handler(user, message, chat_history, model):
                if chunk != "\n\ndata: [DONE]\n\n":
                    yield chunk

            yield "\n\n"

    async def _execute_direct_supervision(
        self, user, message, chat_history, decision, model
    ):
        """Execute direct supervision"""

        # Only show breakdown for truly complex tasks (3+ agents)
        if len(decision.agent_sequence) >= 3:
            logger.info("🔄 Coordinating multiple systems for your request...\n\n")

            # Execute with minimal coordination messages
            for i, agent_name in enumerate(decision.agent_sequence):
                if i > 0:
                    logger.info(f"▶️ Consulting {agent_name}...")

                agent_handler = self.agent_handlers.get(
                    agent_name, self.agent_handlers["general_agent"]
                )

                async for chunk in agent_handler(user, message, chat_history, model):
                    if chunk != "\n\ndata: [DONE]\n\n":
                        yield chunk
        else:
            # For simpler tasks, just route to primary agent
            primary_agent = (
                decision.agent_sequence[0]
                if decision.agent_sequence
                else "general_agent"
            )
            agent_handler = self.agent_handlers.get(
                primary_agent, self.agent_handlers["general_agent"]
            )

            async for chunk in agent_handler(user, message, chat_history, model):
                if chunk != "\n\ndata: [DONE]\n\n":
                    yield chunk

"""
Core supervisor system initialization and main interface
"""

import logging
from typing import Any, Dict, List, Optional

from .coordinator import AgentCoordinator
from .models import SupervisorDecision
from .monitor import AgentPerformanceMonitor, SupervisorMonitor
from .registry import ENHANCED_AGENT_REGISTRY

logger = logging.getLogger(__name__)

# Global instances
performance_monitor = AgentPerformanceMonitor()
supervisor_monitor = SupervisorMonitor()

# Agent handlers - to be set during initialization
_agent_handlers: Optional[Dict[str, Any]] = None
_coordinator: Optional[AgentCoordinator] = None

# System state
_system_initialized = False


def set_agent_handlers(handlers: Dict[str, Any]):
    """Set the agent handlers for the supervisor system"""
    global _agent_handlers, _coordinator

    _agent_handlers = handlers
    _coordinator = AgentCoordinator(handlers)

    logger.info(f"Agent handlers set: {list(handlers.keys())}")


def get_coordinator() -> AgentCoordinator:
    """Get the global coordinator instance"""
    if _coordinator is None:
        raise RuntimeError(
            "Supervisor system not initialized. Call initialize_supervisor_system() first."
        )
    return _coordinator


async def orchestrate_with_supervisor(
    user, message: str, chat_history: List[Dict], model: str = None
):
    """Main orchestration function - use this in your chat service"""

    coordinator = get_coordinator()
    async for chunk in coordinator.execute_supervised_workflow(
        user, message, chat_history, model
    ):
        yield chunk


def validate_supervisor_setup() -> tuple[bool, Dict[str, Any]]:
    """Validate that supervisor is properly configured"""

    validation_results = {
        "agent_registry": len(ENHANCED_AGENT_REGISTRY) > 0,
        "agent_handlers": _agent_handlers is not None and len(_agent_handlers) > 0,
        "performance_monitor": performance_monitor is not None,
        "supervisor_monitor": supervisor_monitor is not None,
        "coordinator": _coordinator is not None,
    }

    all_valid = all(validation_results.values())

    logger.info(f"Supervisor setup validation: {validation_results}")

    if not all_valid:
        missing = [k for k, v in validation_results.items() if not v]
        logger.error(f"Supervisor setup incomplete. Missing: {missing}")

    return all_valid, validation_results


async def initialize_supervisor_system(agent_handlers: Dict[str, Any]) -> bool:
    """Initialize the complete supervisor system"""

    global _system_initialized

    if _system_initialized:
        logger.info("✅ Supervisor system already initialized")
        return True

    try:
        logger.info("🚀 Initializing Enhanced Supervisor System...")

        # Set agent handlers
        set_agent_handlers(agent_handlers)

        # Validate setup
        is_valid, validation = validate_supervisor_setup()
        if not is_valid:
            raise RuntimeError(f"Supervisor setup validation failed: {validation}")

        # Run initial health check
        health_report = await supervisor_monitor.run_health_check(performance_monitor)
        logger.info(f"Initial health check: {health_report}")

        # Log available capabilities
        logger.info("Available agents and capabilities:")
        for agent_name, agent_info in ENHANCED_AGENT_REGISTRY.items():
            logger.info(f"  {agent_name}: {len(agent_info.capabilities)} capabilities")

        _system_initialized = True
        logger.info("✅ Enhanced Supervisor System initialized successfully!")

        return True

    except Exception as e:
        logger.error(f"❌ Failed to initialize supervisor system: {e}")
        return False


def get_supervisor_status() -> Dict[str, Any]:
    """Get current supervisor system status"""

    return {
        "initialized": _system_initialized,
        "performance_monitor": performance_monitor is not None,
        "supervisor_monitor": supervisor_monitor is not None,
        "coordinator": _coordinator is not None,
        "available_agents": list(ENHANCED_AGENT_REGISTRY.keys()),
        "agent_handlers": list(_agent_handlers.keys()) if _agent_handlers else [],
    }


async def get_system_health() -> Dict[str, Any]:
    """Get comprehensive system health report"""

    if not _system_initialized:
        return {
            "status": "not_initialized",
            "message": "Supervisor system not initialized",
        }

    try:
        health_report = await supervisor_monitor.run_health_check(performance_monitor)
        return health_report
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "error", "message": str(e)}


def record_supervisor_performance(
    decision: SupervisorDecision, success: bool, response_time: float
):
    """Record supervisor performance (call this from your chat service)"""

    if supervisor_monitor:
        supervisor_monitor.update_stats(decision, success, response_time)


def record_agent_performance(agent_name: str, success: bool, response_time: float):
    """Record agent performance (call this from your chat service)"""

    if performance_monitor:
        performance_monitor.record_agent_performance(agent_name, success, response_time)


# Configuration
SUPERVISOR_CONFIG = {
    "max_retries": 3,
    "timeout_seconds": 30,
    "enable_escalation": True,
    "enable_complex_workflows": True,
    "enable_performance_monitoring": True,
    "log_level": "INFO",
    "health_check_interval": 300,  # 5 minutes
}


def configure_supervisor(**kwargs) -> Dict[str, Any]:
    """Configure supervisor behavior"""

    global SUPERVISOR_CONFIG
    SUPERVISOR_CONFIG.update(kwargs)

    logger.info(f"Supervisor configuration updated: {SUPERVISOR_CONFIG}")

    return SUPERVISOR_CONFIG

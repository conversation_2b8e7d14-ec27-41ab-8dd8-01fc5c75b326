"""
Performance monitoring for the supervisor system
"""

import logging
from datetime import datetime
from typing import Any, Dict, List

from .models import SupervisorDecision

logger = logging.getLogger(__name__)


class AgentPerformanceMonitor:
    """Monitor individual agent performance"""

    def __init__(self):
        self.performance_history: Dict[str, List[Dict]] = {}
        self.error_counts: Dict[str, int] = {}

    def record_agent_performance(
        self, agent_name: str, success: bool, response_time: float
    ):
        """Record agent performance metrics"""

        if agent_name not in self.performance_history:
            self.performance_history[agent_name] = []
            self.error_counts[agent_name] = 0

        self.performance_history[agent_name].append(
            {
                "success": success,
                "response_time": response_time,
                "timestamp": datetime.now(),
            }
        )

        if not success:
            self.error_counts[agent_name] += 1

        # Keep only last 100 records
        if len(self.performance_history[agent_name]) > 100:
            self.performance_history[agent_name] = self.performance_history[agent_name][
                -100:
            ]

    def get_agent_success_rate(self, agent_name: str) -> float:
        """Get recent success rate for an agent"""

        if agent_name not in self.performance_history:
            return 1.0

        recent_history = self.performance_history[agent_name][-20:]  # Last 20 attempts
        if not recent_history:
            return 1.0

        success_count = sum(1 for record in recent_history if record["success"])
        return success_count / len(recent_history)

    def get_agent_avg_response_time(self, agent_name: str) -> float:
        """Get average response time for an agent"""

        if agent_name not in self.performance_history:
            return 0.0

        recent_history = self.performance_history[agent_name][-20:]
        if not recent_history:
            return 0.0

        total_time = sum(record["response_time"] for record in recent_history)
        return total_time / len(recent_history)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all agents"""

        summary = {}
        for agent_name in self.performance_history.keys():
            summary[agent_name] = {
                "success_rate": f"{self.get_agent_success_rate(agent_name):.1%}",
                "avg_response_time": f"{self.get_agent_avg_response_time(agent_name):.2f}s",
                "total_requests": len(self.performance_history[agent_name]),
                "error_count": self.error_counts.get(agent_name, 0),
            }

        return summary


class SupervisorMonitor:
    """Monitor supervisor system performance"""

    def __init__(self):
        self.supervisor_health = {
            "total_requests": 0,
            "successful_routes": 0,
            "failed_routes": 0,
            "escalations": 0,
            "complex_workflows": 0,
            "avg_response_time": 0.0,
            "agent_utilization": {},
            "last_health_check": datetime.now(),
        }

    def update_stats(
        self, decision: SupervisorDecision, success: bool, response_time: float
    ):
        """Update supervisor performance statistics"""

        self.supervisor_health["total_requests"] += 1

        if success:
            self.supervisor_health["successful_routes"] += 1
        else:
            self.supervisor_health["failed_routes"] += 1

        if decision.action == "escalate":
            self.supervisor_health["escalations"] += 1

        if decision.action in ["chain", "delegate", "handle_directly"]:
            self.supervisor_health["complex_workflows"] += 1

        # Update average response time
        total = self.supervisor_health["total_requests"]
        current_avg = self.supervisor_health["avg_response_time"]
        self.supervisor_health["avg_response_time"] = (
            current_avg * (total - 1) + response_time
        ) / total

        # Update agent utilization
        for agent in decision.agent_sequence:
            if agent not in self.supervisor_health["agent_utilization"]:
                self.supervisor_health["agent_utilization"][agent] = 0
            self.supervisor_health["agent_utilization"][agent] += 1

        self.supervisor_health["last_health_check"] = datetime.now()

    def get_health_report(self) -> Dict[str, Any]:
        """Get comprehensive supervisor health report"""

        total = self.supervisor_health["total_requests"]
        if total == 0:
            return {"status": "no_data", "message": "No requests processed yet"}

        success_rate = self.supervisor_health["successful_routes"] / total
        escalation_rate = self.supervisor_health["escalations"] / total
        complexity_rate = self.supervisor_health["complex_workflows"] / total

        return {
            "status": "healthy"
            if success_rate > 0.9
            else "warning"
            if success_rate > 0.7
            else "critical",
            "success_rate": f"{success_rate:.1%}",
            "escalation_rate": f"{escalation_rate:.1%}",
            "complexity_rate": f"{complexity_rate:.1%}",
            "avg_response_time": f"{self.supervisor_health['avg_response_time']:.2f}s",
            "total_requests": total,
            "agent_utilization": self.supervisor_health["agent_utilization"],
            "last_updated": self.supervisor_health["last_health_check"].isoformat(),
        }

    async def run_health_check(
        self, agent_monitor: AgentPerformanceMonitor
    ) -> Dict[str, Any]:
        """Run comprehensive health check"""

        health_report = self.get_health_report()

        # Add agent performance to health report
        health_report["agent_performance"] = agent_monitor.get_performance_summary()

        return health_report

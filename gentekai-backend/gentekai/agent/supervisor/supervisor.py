"""
Main supervisor agent implementation
"""

import logging
from typing import Any, Dict, List

from pydantic_ai import Agent, RunContext

from gentekai.agent.agent_utils import get_model
from gentekai.agent.prompts.supervisor_prompt import (
    COMPEXITY_ANALYSIS_SYSTEM_PROMPT,
    SUPERVISOR_SYSTEM_PROMPT,
)

from .models import (
    ComplexityAnalysis,
    SupervisorDecision,
    SupervisorDependencies,
    TaskComplexity,
)
from .permission import Permission

logger = logging.getLogger(__name__)


def create_supervisor_agent() -> Agent:
    """Create the supervisor agent that oversees other agents"""

    supervisor = Agent(
        model=get_model(),
        deps_type=SupervisorDependencies,
        output_type=SupervisorDecision,
        retries=2,
        system_prompt=SUPERVISOR_SYSTEM_PROMPT,
    )

    complexity_agent = Agent(
        model=get_model(),
        output_type=ComplexityAnalysis,
        system_prompt=COMPEXITY_ANALYSIS_SYSTEM_PROMPT,
    )

    @supervisor.tool_plain(retries=1)
    async def analyze_request_complexity(
        request: str, user_role: str
    ) -> TaskComplexity:
        """Analyze request complexity using LLM classification"""

        analysis = await complexity_agent.run(
            f"Analyze this request from a {user_role}: {request}"
        )

        logger.info(
            f"analyze_request_complexityAnalyze this request from a {analysis.output}"
        )

        return analysis.output.complexity

    # TODO: maybe we dont need this tool
    # @supervisor.tool(retries=1)
    def check_agent_availability(
        ctx: RunContext[SupervisorDependencies], agent_name: str
    ) -> bool:
        """Check if an agent is available for new tasks"""

        current_load = ctx.deps.current_agent_loads.get(agent_name, 0)
        agent_info = ctx.deps.agent_registry.get(agent_name)

        if not agent_info:
            logger.warning(f"Agent {agent_name} not found in registry")
            return False

        max_concurrent = agent_info.max_concurrent_tasks
        is_available = current_load < max_concurrent

        logger.debug(
            f"Agent {agent_name} availability: {is_available} (load: {current_load}/{max_concurrent})"
        )
        return is_available

    # TODO: maybe we dont need this tool
    # @supervisor.tool(retries=1)
    def get_agent_performance(
        ctx: RunContext[SupervisorDependencies], agent_name: str
    ) -> float:
        """Get the recent performance score for an agent only"""

        # Default to registry success rate if no recent performance data
        agent_info = ctx.deps.agent_registry.get(agent_name)
        if not agent_info:
            logger.warning(f"Agent {agent_name} not found in registry")
            return 0.5

        performance = ctx.deps.agent_performance.get(
            agent_name, agent_info.success_rate
        )

        return performance

    @supervisor.tool(retries=1)
    def check_user_permissions(
        ctx: RunContext[SupervisorDependencies], required_permissions: List[Permission]
    ) -> bool:
        """
        Check user permissions using predefined permission enum values.

        Args:
            required_permissions: List of Permission enum values
        """
        user_role = ctx.deps.user_role
        perm_strings = [perm.value for perm in required_permissions]
        role_permissions = {
            "user": [Permission.READ_PROFILE.value, Permission.WRITE_PROFILE.value],
            "admin": [perm.value for perm in Permission],  # All permissions
            "manager": [
                Permission.READ_PROFILE.value,
                Permission.WRITE_PROFILE.value,
                Permission.HR_READ.value,
                Permission.FINANCE_READ.value,
                Permission.EXPENSE_WRITE.value,
            ],
            "hr": [
                Permission.READ_PROFILE.value,
                Permission.HR_READ.value,
                Permission.HR_WRITE.value,
            ],
        }

        user_permissions = role_permissions.get(
            user_role, [Permission.READ_PROFILE.value]
        )
        has_permissions = all(perm in user_permissions for perm in perm_strings)

        logger.debug(
            f"User {ctx.deps.user_id} ({user_role}) permissions check: {has_permissions}"
        )
        return has_permissions

    @supervisor.tool_plain(retries=0)
    def escalate_to_human(reason: str, context: str) -> str:
        """Escalate complex issues to human oversight"""

        logger.warning(f"Escalating to human: {reason}")
        logger.info(f"Escalation context: {context}")

        return f"Task escalated to human supervisor. Reason: {reason}"

    @supervisor.tool(retries=1)
    def get_agent_capabilities(
        ctx: RunContext[SupervisorDependencies], agent_name: str
    ) -> Dict[str, Any]:
        """Get detailed capabilities of a specific agent"""

        agent_info = ctx.deps.agent_registry.get(agent_name)
        if not agent_info:
            return {"error": f"Agent {agent_name} not found"}

        capabilities = {
            "name": agent_info.name,
            "description": agent_info.description,
            "capabilities": [
                {
                    "name": cap.name,
                    "description": cap.description,
                    "complexity": cap.complexity_level,
                    "permissions": cap.required_permissions,
                }
                for cap in agent_info.capabilities
            ],
            "success_rate": agent_info.success_rate,
            "fallback_agents": agent_info.fallback_agents,
        }

        logger.info("Agent capabilities: %s", capabilities)

        return capabilities

    return supervisor

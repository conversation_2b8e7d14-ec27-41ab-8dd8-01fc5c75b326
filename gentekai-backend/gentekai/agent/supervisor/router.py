"""
Enhanced routing with supervisor decision making
"""

import logging
from typing import Any, Dict, List

from .models import SupervisorDecision, SupervisorDependencies
from .registry import ENHANCED_AGENT_REGISTRY
from .supervisor import create_supervisor_agent

logger = logging.getLogger(__name__)


class EnhancedRouter:
    """Enhanced router with supervisor decision making"""

    def __init__(self):
        self.supervisor = create_supervisor_agent()
        self.routing_cache = {}

    async def route_with_supervision(
        self, user, message: str, chat_history: List[Dict]
    ) -> SupervisorDecision:
        """Route request through supervisor for intelligent decision making"""

        deps = SupervisorDependencies(
            user_id=str(user.id),
            user_role=getattr(user, "role", "user"),
            conversation_id=getattr(user, "conversation_id", "default"),
            chat_history=chat_history,
            agent_registry=ENHANCED_AGENT_REGISTRY,
        )
        enhanced_prompt = f"""
        New User Request: {message}
        User Role: {deps.user_role}

        Analyze this request and decide how to handle it:
        1. Should it go to a single agent or require multiple agents?
        2. What's the complexity level?
        3. Are there any dependencies or coordination needs?
        4. What agents would be best suited for this task?

        Available agents and their capabilities:
        {self._format_agent_capabilities()}
        """

        try:
            result = await self.supervisor.run(
                user_prompt=enhanced_prompt,
                deps=deps,
            )

            logger.info(f"Supervisor decision made successfully: {result}")

            return result.output

        except Exception as e:
            logger.error(f"Supervisor routing failed: {e}")
            # Fallback to simple routing
            return SupervisorDecision(
                action="route",
                agent_sequence=["general_agent"],
                coordination_needed=False,
                monitoring_level="detailed",
                success_criteria="Should it go to a single agent or require multiple agents? It should make effective, fast and secure decision",
            )

    @staticmethod
    def _format_agent_capabilities() -> str:
        """Format agent capabilities for the supervisor prompt"""

        lines = []
        for agent_name, agent_info in ENHANCED_AGENT_REGISTRY.items():
            lines.append(f"- {agent_name}: {agent_info.description}")
            for cap in agent_info.capabilities:
                lines.append(
                    f"  * {cap.name}: {cap.description} ({cap.complexity_level})"
                )

        return "\n".join(lines)

    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics"""

        return {
            "cache_size": len(self.routing_cache),
            "supervisor_initialized": self.supervisor is not None,
            "available_agents": list(ENHANCED_AGENT_REGISTRY.keys()),
        }

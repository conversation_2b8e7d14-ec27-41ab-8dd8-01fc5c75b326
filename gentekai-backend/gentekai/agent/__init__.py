# ===== 1. Updated app/agent/__init__.py =====
"""
Initialize the supervisor system at the agent package level
"""

from .specialized.employee_agent import handle as employee_agent_handle
from .specialized.general_agent import handle as general_agent_handle
from .specialized.hr_agent import handle_hr_request as hr_agent_handle

# Import supervisor system
from .supervisor import initialize_supervisor_system

# Define agent handlers for supervisor
AGENT_HANDLERS = {
    "employee_agent": employee_agent_handle,
    "hr_agent": hr_agent_handle,
    "general_agent": general_agent_handle,
}


# Initialize supervisor (will be called at startup)
async def init_agent_system():
    """Initialize the agent system with supervisor"""
    success = await initialize_supervisor_system(AGENT_HANDLERS)
    if success:
        print("✅ Agent system with supervisor initialized successfully")
    else:
        print("❌ Failed to initialize agent system")
    return success

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic_ai.mcp import MCPServerSSE
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    TextPart,
    UserPromptPart,
)
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

from gentekai.config import settings
from gentekai.services.document_context_service import DocumentContextService
from gentekai.services.universal_document_service import UniversalDocumentService

logger = logging.getLogger(__name__)


def get_model():
    return OpenAIModel(
        model_name=settings.GENERAL_LLM_MODEL,
        provider=OpenRouterProvider(api_key=settings.OPENROUTER_API_KEY),
    )


def get_mcp_server():
    return MCPServerSSE(url=settings.SQL_TOOL_URL)


def convert_chat_history(
    original_chat_history: List[Dict[str, Any]],
) -> List[ModelMessage]:
    """Convert OpenAI-style chat history to pydantic-ai message format"""
    if not original_chat_history:
        return []

    chat_history = optimize_chat_history(original_chat_history)

    logger.info(f"Converted chat history to pydantic-ai message format: {chat_history}")

    converted_messages = []

    for message in chat_history:
        role = message.get("role", "").lower()
        content = message.get("content", "")
        logger.error(f"Role {role} content {message}")

        if not content:
            continue

        current_time = datetime.now(timezone.utc)
        context_content = content

        if role == "user":
            converted_messages.append(
                ModelRequest(
                    parts=[
                        UserPromptPart(
                            content=context_content,
                            timestamp=current_time,
                        )
                    ]
                )
            )
        elif role == "assistant":
            converted_messages.append(
                ModelResponse(
                    parts=[TextPart(content=content)],
                    model_name="assistant",
                    timestamp=current_time,
                )
            )
        elif role == "system":
            converted_messages.append(
                ModelRequest(
                    parts=[SystemPromptPart(content=content, timestamp=current_time)]
                )
            )

    return converted_messages


def optimize_chat_history(
    chat_history, max_messages=20, max_content_length=500, max_doc_context=200
):
    """
    - Remove empty/null fields
    - Truncate long content and document context
    - Keep only recent messages
    """
    recent_messages = (
        chat_history[-max_messages:]
        if len(chat_history) > max_messages
        else chat_history
    )

    # Step 2: Clean and truncate each message (approaches 1 & 3)
    optimized = []
    for message in recent_messages:
        # Start with essential fields and truncate content
        clean_msg = {
            "role": message["role"],
            "content": message["content"][:max_content_length],
        }

        # Add ellipsis if content was truncated
        if len(message["content"]) > max_content_length:
            clean_msg["content"] += "..."

        # Only include non-empty file_keys (approach 1)
        if message.get("file_keys") and len(message["file_keys"]) > 0:
            clean_msg["file_keys"] = message["file_keys"]

        # Only include non-empty document_context and truncate it (approaches 1 & 3)
        if message.get("document_context") and message["document_context"].strip():
            doc_context = message["document_context"][:max_doc_context]
            if len(message["document_context"]) > max_doc_context:
                doc_context += "..."
            clean_msg["document_context"] = doc_context

        optimized.append(clean_msg)

    return optimized


async def extract_document_data(
    file_keys: List[str],
    extraction_type: str = "auto",  # auto, text_only, structured, summary
    context_prompt: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Extract and process document data from R2 storage

    Args:
        file_keys: List of file keys in R2 storage
        extraction_type: Type of extraction (auto, text_only, structured, summary)
        context_prompt: Optional context for focused extraction

    Returns:
        Dictionary with extracted document data
    """

    doc_service = UniversalDocumentService()
    results = {
        "extracted_documents": [],
        "total_files": len(file_keys),
        "successful_extractions": 0,
        "failed_extractions": 0,
        "combined_text": "",
        "document_summaries": [],
    }

    for file_key in file_keys:
        try:
            # Get document info first
            doc_info = doc_service.get_document_info(file_key)

            if not doc_info["supported"]:
                results["failed_extractions"] += 1
                results["extracted_documents"].append(
                    {
                        "file_key": file_key,
                        "status": "unsupported",
                        "error": f"Unsupported file type: {doc_info['document_type']}",
                    }
                )
                continue

            # Process document based on extraction type
            if extraction_type == "text_only":
                extracted_text = doc_service.extract_text_from_r2(file_key)
                document_data = {
                    "file_key": file_key,
                    "filename": file_key.split("/")[-1],
                    "document_type": doc_info["document_type"],
                    "content": extracted_text,
                    "file_size": doc_info["file_size"],
                    "extraction_method": "text_only",
                }

            elif extraction_type == "structured":
                # Full structured extraction
                document = doc_service.process_document_from_r2(file_key)
                document_data = {
                    "file_key": file_key,
                    "filename": document.filename,
                    "document_type": document.file_type.value,
                    "content": document.content,
                    "file_size": document.file_size,
                    "metadata": document.metadata,
                    "has_text_content": document.has_text_content,
                    "has_scanned_content": document.has_scanned_content,
                    "extraction_method": "structured",
                }

                # Add page info for PDFs
                if document.pages:
                    document_data["page_count"] = len(document.pages)
                    document_data["pages"] = [
                        {
                            "page_number": page.page_number,
                            "content_preview": page.content[:200] + "..."
                            if len(page.content) > 200
                            else page.content,
                            "source": page.source.value,
                        }
                        for page in document.pages[:5]  # First 5 pages only
                    ]

                # Add sheet info for Excel files
                if document.sheets:
                    document_data["sheet_count"] = len(document.sheets)
                    document_data["sheets"] = [
                        {
                            "name": sheet.name,
                            "rows": len(sheet.data),
                            "columns": len(sheet.headers) if sheet.headers else 0,
                            "headers": sheet.headers[:10]
                            if sheet.headers
                            else [],  # First 10 headers
                        }
                        for sheet in document.sheets
                    ]

            elif extraction_type == "summary" and context_prompt:
                # Use document context service for intelligent summary
                context_service = DocumentContextService()
                context_data = await context_service.prepare_document_context(
                    file_keys=[file_key],
                    user_prompt=context_prompt,
                    max_context_length=2000,
                )

                document_data = {
                    "file_key": file_key,
                    "filename": file_key.split("/")[-1],
                    "document_type": doc_info["document_type"],
                    "content": context_data["context"],
                    "file_size": doc_info["file_size"],
                    "extraction_method": "summary",
                    "context_strategy": context_data["strategy_used"],
                }

            else:  # auto
                # Smart extraction based on file type and size
                if doc_info["file_size"] < 50000:  # Small files - full extraction
                    document = doc_service.process_document_from_r2(file_key)
                    document_data = {
                        "file_key": file_key,
                        "filename": document.filename,
                        "document_type": document.file_type.value,
                        "content": document.content,
                        "file_size": document.file_size,
                        "extraction_method": "auto_full",
                    }
                else:  # Large files - text only
                    extracted_text = doc_service.extract_text_from_r2(file_key)
                    document_data = {
                        "file_key": file_key,
                        "filename": file_key.split("/")[-1],
                        "document_type": doc_info["document_type"],
                        "content": extracted_text[:5000] + "..."
                        if len(extracted_text) > 5000
                        else extracted_text,
                        "file_size": doc_info["file_size"],
                        "extraction_method": "auto_truncated",
                    }

            results["extracted_documents"].append(document_data)
            results["successful_extractions"] += 1

            # Add to combined text
            if document_data.get("content"):
                results["combined_text"] += f"\n--- {document_data['filename']} ---\n"
                results["combined_text"] += document_data["content"] + "\n\n"

            # Create summary
            content_preview = document_data.get("content", "")[:300]
            results["document_summaries"].append(
                {
                    "filename": document_data["filename"],
                    "type": document_data["document_type"],
                    "size": document_data["file_size"],
                    "preview": content_preview + "..."
                    if len(content_preview) == 300
                    else content_preview,
                }
            )

        except Exception as e:
            results["failed_extractions"] += 1
            results["extracted_documents"].append(
                {"file_key": file_key, "status": "error", "error": str(e)}
            )

    return results

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional
from urllib.parse import quote

from pydantic import BaseModel, Field, ValidationError
from pydantic_ai import Agent, ModelRetry

from gentekai.agent.agent_utils import convert_chat_history, get_mcp_server, get_model
from gentekai.agent.prompts.employee_agent_prompt import SYSTEM_PROMPT

logger = logging.getLogger(__name__)


# Updated Dependencies class to match the enhanced agent
class Dependencies:
    def __init__(self, user_id: str, role: str = "user", **kwargs):
        self.user_id = user_id
        self.role = role
        self.context = kwargs


class ConfidenceLevel(str, Enum):
    """Enumeration for confidence levels"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class TaskStatus(str, Enum):
    """Enumeration for task completion status"""

    COMPLETED = "completed"
    PARTIALLY_COMPLETED = "partially_completed"
    FAILED = "failed"
    REQUIRES_FOLLOWUP = "requires_followup"


class QualityMetrics(BaseModel):
    """Quality assessment metrics"""

    accuracy: float = Field(ge=0.0, le=1.0, description="Accuracy score 0-1")
    completeness: float = Field(ge=0.0, le=1.0, description="Completeness score 0-1")
    clarity: float = Field(ge=0.0, le=1.0, description="Response clarity score 0-1")
    overall_score: float = Field(ge=0.0, le=1.0, description="Overall quality score")


class TaskCompletionResult(BaseModel):
    """Enhanced structured result with comprehensive evaluation"""

    user_response: str = Field(description="The actual response shown to the user")
    task_status: TaskStatus = Field(description="Status of task completion")
    confidence_level: ConfidenceLevel = Field(
        description="Agent's confidence in the response"
    )
    reasoning: str = Field(description="Detailed reasoning behind the response")
    potential_issues: List[str] = Field(
        default_factory=list, description="Identified potential issues"
    )
    improvement_suggestions: List[str] = Field(
        default_factory=list, description="Suggestions for improvement"
    )
    quality_metrics: Optional[QualityMetrics] = Field(
        None, description="Quality assessment metrics"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class AgentDependencies(BaseModel):
    """Dependencies for the agent"""

    user_id: str = Field(description="Valid UUID for the user")
    session_id: Optional[str] = Field(None, description="Session identifier")
    context: Dict[str, Any] = Field(
        default_factory=dict, description="Additional context"
    )


def validate_user_id(user_id: str) -> bool:
    """Validate if user_id is a proper UUID"""
    try:
        uuid.UUID(user_id)
        return True
    except ValueError:
        return False


def create_self_evaluating_agent(user_id: str, session_id: Optional[str] = None):
    """
    Create an enhanced agent with comprehensive self-evaluation capabilities

    Args:
        user_id: Valid UUID string for the user
    """

    # Validate user_id upfront
    if not validate_user_id(user_id):
        raise ValueError(f"Invalid user_id format: {user_id}. Must be a valid UUID.")

    # Enhanced system prompt with clearer instructions
    system_prompt = SYSTEM_PROMPT.format(user_id=user_id)

    employee_agent = Agent(
        model=get_model(),
        mcp_servers=[get_mcp_server()],
        deps_type=AgentDependencies,
        output_type=TaskCompletionResult,
        retries=3,
        system_prompt=system_prompt,
    )

    @employee_agent.tool_plain(retries=2)
    def self_reflect(
        current_approach: str,
        identified_issues: List[str],
        improvement_areas: List[str],
        severity_level: str = "medium",
    ) -> str:
        """
        Enhanced self-reflection tool for the agent

        Args:
            current_approach: Description of current approach/reasoning
            identified_issues: List of specific issues identified
            improvement_areas: Areas that could be improved
            severity_level: Severity of issues (low, medium, high, critical)
        """
        logger.info(f"Agent self-reflection for user {user_id}")
        logger.info(f"Approach: {current_approach}")
        logger.info(f"Issues: {identified_issues}")
        logger.info(f"Improvements: {improvement_areas}")
        logger.info(f"Severity: {severity_level}")

        # Trigger retry for critical issues or too many problems
        if severity_level == "critical" or len(identified_issues) > 5:
            raise ModelRetry(
                f"Critical issues detected (severity: {severity_level}). "
                f"Issues: {', '.join(identified_issues)}. Please revise your approach."
            )

        # Warning for multiple issues
        if len(identified_issues) > 3:
            logger.warning(f"Multiple issues identified: {identified_issues}")

        return f"Self-reflection completed. Severity: {severity_level}. Improvements needed: {', '.join(improvement_areas)}"

    @employee_agent.tool_plain(retries=1)
    def validate_task_quality(
        task_description: str,
        expected_outcome: str,
        user_satisfaction_estimate: float = 0.8,
    ) -> str:
        """
        Enhanced quality validation tool

        Args:
            task_description: Description of the completed task
            expected_outcome: What the expected outcome should be
            user_satisfaction_estimate: Estimated user satisfaction (0-1)
        """
        quality_issues = []
        quality_score = 1.0

        # Basic validation checks
        if len(task_description.strip()) < 10:
            quality_issues.append("Task description too brief")
            quality_score -= 0.3

        if any(
            word in task_description.lower()
            for word in ["error", "failed", "couldn't", "unable"]
        ):
            quality_issues.append("Task indicates potential failure")
            quality_score -= 0.4

        if len(expected_outcome.strip()) < 5:
            quality_issues.append("Expected outcome unclear")
            quality_score -= 0.2

        if user_satisfaction_estimate < 0.6:
            quality_issues.append("Low estimated user satisfaction")
            quality_score -= 0.3

        # Trigger retry for poor quality
        if quality_score < 0.5 or len(quality_issues) > 2:
            raise ModelRetry(
                f"Quality validation failed (score: {quality_score:.2f}). "
                f"Issues: {', '.join(quality_issues)}. Please improve the response."
            )

        logger.info(f"Quality validation passed with score: {quality_score:.2f}")
        return f"Quality validation passed. Score: {quality_score:.2f}. Minor issues: {', '.join(quality_issues) if quality_issues else 'None'}"

    @employee_agent.tool_plain(retries=0)
    def calculate_quality_metrics(
        response_text: str,
        task_complexity: str = "medium",
        information_accuracy: float = 0.9,
    ) -> QualityMetrics:
        """
        Calculate comprehensive quality metrics

        Args:
            response_text: The response text to evaluate
            task_complexity: Complexity level (simple, medium, complex)
            information_accuracy: Estimated accuracy (0-1)
        """
        # Simple heuristic-based quality assessment
        word_count = len(response_text.split())

        # Completeness based on response length and complexity
        complexity_multiplier = {"simple": 0.8, "medium": 1.0, "complex": 1.2}.get(
            task_complexity, 1.0
        )
        expected_length = 50 * complexity_multiplier
        completeness = min(1.0, word_count / expected_length)

        # Clarity based on sentence structure and readability
        sentences = response_text.split(".")
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(
            len(sentences), 1
        )
        clarity = max(
            0.5, min(1.0, 1.0 - (avg_sentence_length - 15) / 30)
        )  # Optimal ~15 words/sentence

        # Overall score
        overall_score = (information_accuracy + completeness + clarity) / 3

        return QualityMetrics(
            accuracy=information_accuracy,
            completeness=completeness,
            clarity=clarity,
            overall_score=overall_score,
        )

    @employee_agent.tool_plain(retries=0)
    def debug_context(include_sensitive: bool = False) -> str:
        """
        Debug tool to inspect current context and configuration

        Args:
            include_sensitive: Whether to include potentially sensitive debug info
        """
        debug_info = {
            "user_id_valid": validate_user_id(user_id),
            "user_id_format": "UUID" if validate_user_id(user_id) else "Invalid",
            "agent_retries": 3,
            "tools_available": [
                "self_reflect",
                "validate_task_quality",
                "calculate_quality_metrics",
            ],
        }

        if include_sensitive:
            debug_info["user_id"] = user_id

        logger.debug(f"Debug context: {debug_info}")
        return f"Debug info: {debug_info}"

    @employee_agent.tool_plain(retries=1)
    def get_calendar_link(
        event_title: str,
        date: str,  # YYYY-MM-DD
        time: str,  # HH:MM (24-hour)
        duration_hours: int = 1,
        description: str = "",
        location: str = "",
    ) -> str:
        """
        Generate Google Calendar link.

        Returns the raw URL - agent should format it nicely for the user.
        """
        try:
            # Calculate end time
            start_hour, start_min = map(int, time.split(":"))
            end_hour = start_hour + duration_hours
            end_min = start_min

            if end_hour >= 24:
                end_hour = 23
                end_min = 59

            # Format for Google Calendar
            start_dt = f"{date.replace('-', '')}T{start_hour:02d}{start_min:02d}00Z"
            end_dt = f"{date.replace('-', '')}T{end_hour:02d}{end_min:02d}00Z"

            # Build URL
            url = "https://calendar.google.com/calendar/render?action=TEMPLATE"
            url += f"&text={quote(event_title)}"
            url += f"&dates={start_dt}/{end_dt}"

            if description:
                url += f"&details={quote(description)}"
            if location:
                url += f"&location={quote(location)}"

            return url

        except Exception as e:
            return f"ERROR: {str(e)}"

    @employee_agent.tool_plain(retries=0)
    def create_quick_calendar_link(event_details: str) -> str:
        """
        Create a calendar link from natural language event description.
        Let the agent parse the details and call create_google_calendar_link with proper format.

        Args:
            event_details: Natural language description like "Team meeting tomorrow 2pm for 1 hour"

        Returns:
            Instructions for the agent to parse and create the calendar link
        """
        return f"""I need to parse these event details and create a calendar link: "{event_details}"

    Please extract:
    1. Event title
    2. Date in YYYY-MM-DD format  
    3. Start time in HH:MM format (24-hour)
    4. End time in HH:MM format (if specified)
    5. Any location or description

    Then use the get_calendar_link tool with the properly formatted information."""

    return employee_agent


async def handle_employee_request_with_self_evaluation(
    user,
    message: str,
    chat_history: list,
    model_name: Optional[str] = None,
    session_id: Optional[str] = None,
):
    """Handle requests with the enhanced self-evaluation agent"""

    try:
        user_id = str(getattr(user, "id", user))
        logger.info(f"Processing self-evaluating request for user {user_id}: {message}")

        # Validate UUID format (enhanced agent does this automatically now)
        try:
            uuid.UUID(user_id)
            logger.info(f"User ID {user_id} is valid UUID format")
        except ValueError:
            logger.error(f"User ID {user_id} is NOT valid UUID format")
            yield f"Error: Invalid user ID format. Expected UUID, got: {user_id}"
            return

        # Create the enhanced agent
        agent = create_self_evaluating_agent(user_id, session_id)

        # Create dependencies using the enhanced Dependencies class
        deps = AgentDependencies(
            user_id=user_id,
            session_id=session_id,
            context={
                "role": getattr(user, "role", "user"),
                "model_name": model_name,
                "request_timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        logger.info(f"Dependencies created with user_id: {deps.user_id}")

        # Convert chat history
        converted_history = convert_chat_history(chat_history or [])
        logger.debug(
            f"Converted {len(chat_history or [])} messages to pydantic-ai format"
        )

        async with agent.run_mcp_servers():
            # The enhanced agent returns TaskCompletionResult with comprehensive evaluation
            result = await agent.run(
                user_prompt=message, deps=deps, message_history=converted_history
            )

            # Extract the enhanced evaluation result
            evaluation_result: TaskCompletionResult = result.output

            # Enhanced logging with new fields
            logger.info("Enhanced self-evaluation completed:")
            logger.info(f"  Task status: {evaluation_result.task_status}")
            logger.info(f"  Confidence: {evaluation_result.confidence_level}")
            logger.info(f"  Reasoning: {evaluation_result.reasoning}")
            logger.info(f"  Potential issues: {evaluation_result.potential_issues}")
            logger.info(
                f"  Improvement suggestions: {evaluation_result.improvement_suggestions}"
            )

            # Log quality metrics if available
            if evaluation_result.quality_metrics:
                metrics = evaluation_result.quality_metrics
                logger.info(
                    f"  Quality metrics - Overall: {metrics.overall_score:.2f}, "
                    f"Accuracy: {metrics.accuracy:.2f}, "
                    f"Completeness: {metrics.completeness:.2f}, "
                    f"Clarity: {metrics.clarity:.2f}"
                )

            # Handle different task statuses
            response_text = evaluation_result.user_response

            # Add status indicators for partial completion or issues
            if evaluation_result.task_status == "partially_completed":
                response_text += "\n\n*Note: This response partially addresses your request. Some aspects may need follow-up.*"
            elif evaluation_result.task_status == "requires_followup":
                response_text += "\n\n*Note: This may require additional information or clarification from you.*"
            elif evaluation_result.task_status == "failed":
                response_text = f"I apologize, but I wasn't able to complete this task successfully. {response_text}"

            # Enhanced streaming with confidence indicators
            words = response_text.split()

            # Add confidence indicator for low confidence responses
            if evaluation_result.confidence_level == "low":
                # yield "*[Low confidence response]* "
                await asyncio.sleep(0.1)

            for word in words:
                yield word + " "
                await asyncio.sleep(0.05)

            # Optionally surface improvement suggestions for debugging
            if evaluation_result.improvement_suggestions and logger.isEnabledFor(
                logging.DEBUG
            ):
                logger.debug(
                    f"Improvement suggestions: {evaluation_result.improvement_suggestions}"
                )

            logger.info("Successfully streamed enhanced self-evaluated response")

    except ValidationError as e:
        logger.error(f"Enhanced validation error: {e}")
        yield "I need to reconsider my approach due to validation issues. Let me try a different approach."
    except ValueError as e:
        logger.error(f"Value error (likely UUID): {e}")
        yield f"There was an issue with the request format: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error in enhanced handler: {e}")
        yield f"I encountered an unexpected error during processing: {str(e)}"

    yield "\n\ndata: [DONE]\n\n"


# Legacy compatibility function
async def handle(user, message, chat_history, model_name: Optional[str] = None):
    """Enhanced legacy function with comprehensive self-evaluation"""
    async for chunk in handle_employee_request_with_self_evaluation(
        user, message, chat_history, model_name
    ):
        yield chunk

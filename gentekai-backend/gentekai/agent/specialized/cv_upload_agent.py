"""
CV Upload Agent - Specialized agent for handling CV file uploads from Slack
This agent is specifically designed to handle CV uploads and prevent duplicate creation
"""

import logging
from typing import Any, Dict, List, Optional
from enum import Enum
from pydantic import BaseModel, Field

from gentekai.agent.agent_utils import (
    convert_chat_history,
    get_mcp_server,
    get_model,
)
from gentekai.db.models import UserRole
from pydantic_ai import Agent

logger = logging.getLogger(__name__)


class CVUploadStatus(str, Enum):
    """Status of CV upload task"""
    COMPLETED = "completed"
    FAILED = "failed"


class CVUploadResult(BaseModel):
    """Result of CV upload task"""
    user_response: str = Field(description="Response to show the user")
    task_status: CVUploadStatus = Field(description="Status of the upload")
    cv_id: Optional[str] = Field(default=None, description="ID of created CV")
    success: bool = Field(description="Whether upload was successful")


class CVUploadDependencies(BaseModel):
    """Dependencies for CV upload agent"""
    user_id: str = Field(description="User ID who uploaded the file")
    user_role: UserRole = Field(description="User role")
    document_context: Optional[str] = Field(default=None, description="Document content")


CV_UPLOAD_SYSTEM_PROMPT = """
You are a specialized CV Upload Agent for user {user_id}.

SINGLE PURPOSE: Create ONE CV from uploaded document for the current user.

CRITICAL RULES:
1. Use ONLY the create_cv_from_slack_upload tool
2. NEVER use create_user tool - user already exists
3. NEVER use create_cv_data tool - use create_cv_from_slack_upload instead
4. Create ONLY ONE CV per request
5. User ID is always '{user_id}' - this user already exists

PROCESS:
1. Extract CV information from document context
2. Call create_cv_from_slack_upload with user_id='{user_id}'
3. Set is_public based on user request
4. Return success message with CV ID

FORBIDDEN:
- Creating new users
- Using create_cv_data tool
- Creating multiple CVs
- Any other MCP tools except create_cv_from_slack_upload
"""


def create_cv_upload_agent(user_id: str, user_role: UserRole = UserRole.USER):
    """Create specialized CV upload agent"""
    
    system_prompt = CV_UPLOAD_SYSTEM_PROMPT.format(user_id=user_id)
    
    logger.info(f"Creating CV Upload Agent for user {user_id}")
    
    agent = Agent(
        model=get_model(),
        mcp_servers=[get_mcp_server()],
        deps_type=CVUploadDependencies,
        output_type=CVUploadResult,
        retries=1,  # No retries to prevent duplicates
        system_prompt=system_prompt,
    )
    
    return agent


async def handle_cv_upload_request(
    user,
    message: str,
    chat_history: list,
    document_context: Optional[str] = None,
    model_name: Optional[str] = None,
):
    """Handle CV upload requests using specialized agent"""
    
    try:
        user_id = str(getattr(user, "id", user))
        user_role = UserRole(getattr(user, "role", UserRole.USER.value))
        
        logger.info(f"Processing CV upload for user {user_id}: {message}")
        
        # Create specialized CV upload agent
        agent = create_cv_upload_agent(user_id, user_role)
        
        # Create dependencies
        deps = CVUploadDependencies(
            user_id=user_id,
            user_role=user_role,
            document_context=document_context,
        )
        
        # Convert chat history
        converted_history = convert_chat_history(chat_history or [])
        
        # Run the agent
        async with agent.run_mcp_servers():
            result = await agent.run(
                user_prompt=message,
                deps=deps,
                message_history=converted_history
            )
            
            cv_result: CVUploadResult = result.output
            
            logger.info(f"CV upload completed: {cv_result.task_status}")
            logger.info(f"CV ID: {cv_result.cv_id}")
            
            # Stream the response
            yield cv_result.user_response
            
    except Exception as e:
        logger.error(f"CV upload failed: {e}")
        yield f"Lỗi khi tải CV lên hệ thống: {str(e)}"

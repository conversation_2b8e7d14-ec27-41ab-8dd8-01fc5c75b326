import logging
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, ValidationError
from pydantic_ai import Agent, ModelRetry

from gentekai.agent.agent_utils import (
    convert_chat_history,
    extract_document_data,
    get_mcp_server,
    get_model,
)
from gentekai.agent.prompts.hr_agent_prompt import SYSTEM_PROMPT
from gentekai.db.models import UserRole

logger = logging.getLogger(__name__)


class HRTaskStatus(str, Enum):
    """Status of HR task completion"""

    COMPLETED = "completed"
    PARTIALLY_COMPLETED = "partially_completed"
    FAILED = "failed"
    REQUIRES_APPROVAL = "requires_approval"


class HRTaskResult(BaseModel):
    """Simplified HR task result"""

    user_response: str = Field(description="The response shown to the user")
    task_status: HRTaskStatus = Field(description="Status of task completion")
    confidence_level: str = Field(description="Agent's confidence in the response")
    entities_created: List[str] = Field(
        default_factory=list, description="IDs of created entities"
    )
    requires_followup: bool = Field(
        default=False, description="Whether followup is needed"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class HRDependencies(BaseModel):
    """Dependencies for the HR agent"""
    user_id: str = Field(description="Valid UUID for the user")
    user_role: UserRole = Field(description="User's role (owner/admin)")
    session_id: Optional[str] = Field(None, description="Session identifier")
    context: Dict[str, Any] = Field(
        default_factory=dict, description="Additional context"
    )


def validate_user_id(user_id: str) -> bool:
    """Validate if user_id is a proper UUID"""
    try:
        uuid.UUID(user_id)
        return True
    except ValueError:
        return False


def create_hr_agent(
    user_id: str, user_role: UserRole = UserRole.ADMIN, session_id: Optional[str] = None
):
    """
    Create a generic HR agent that discovers and uses MCP tools dynamically
    """

    # Generic HR system prompt that doesn't hardcode specific tools
    system_prompt = SYSTEM_PROMPT.format(user_id=user_id, user_role=user_role)

    logger.info(
        f"Creating HR agent for user {user_id} with role {user_role.value} and session {session_id}"
    )
    logger.info(f"DEBUG: System prompt contains user_id: {user_id}")
    logger.info(f"DEBUG: System prompt preview: {system_prompt[:500]}...")

    hr_agent = Agent(
        model=get_model(),
        mcp_servers=[get_mcp_server()],
        deps_type=HRDependencies,
        output_type=HRTaskResult,
        retries=3,
        system_prompt=system_prompt,
        
    )

    @hr_agent.tool_plain(retries=2)
    def parse_date_flexibly(date_input: Any) -> Optional[str]:
        """
        Parse various date inputs and return ISO format string

        Args:
            date_input: Date in various formats (string, dict, etc.)

        Returns:
            ISO format date string or None
        """
        if not date_input:
            return None

        # If already a string, try to parse it
        if isinstance(date_input, str):
            # Try different date formats
            date_formats = [
                "%Y-%m-%d",
                "%m/%d/%Y",
                "%d/%m/%Y",
                "%Y/%m/%d",
                "%B %d, %Y",
                "%d %B %Y",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S.%fZ",
            ]

            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_input.strip(), fmt)
                    return parsed_date.isoformat()
                except ValueError:
                    continue

            # If it's already ISO format, return as is
            if "T" in date_input and ("-" in date_input or "/" in date_input):
                return date_input

        # If it's a datetime object
        elif hasattr(date_input, "isoformat"):
            return date_input.isoformat()

        # If it's a dict with date components
        elif isinstance(date_input, dict):
            try:
                year = date_input.get("year")
                month = date_input.get("month")
                day = date_input.get("day")
                if year and month and day:
                    date_obj = datetime(int(year), int(month), int(day))
                    return date_obj.isoformat()
            except Exception:
                pass

        logger.warning(f"Could not parse date: {date_input}")
        return None

    @hr_agent.tool_plain(retries=2)
    def format_entity_display(entity_type: str, entity_data: Dict[str, Any]) -> str:
        """
        Format any entity for user-friendly display

        Args:
            entity_type: Type of entity (position, employee, etc.)
            entity_data: Entity data from MCP server
        """
        output = [f"**{entity_type.title()} Details:**\n"]

        # Common fields to display first
        priority_fields = ["id", "title", "name", "status", "type", "level"]

        # Display priority fields first
        for field in priority_fields:
            if field in entity_data and entity_data[field]:
                display_name = field.replace("_", " ").title()
                value = entity_data[field]

                # Handle nested objects
                if isinstance(value, dict) and "name" in value:
                    value = value["name"]
                elif isinstance(value, dict) and "title" in value:
                    value = value["title"]

                output.append(f"**{display_name}:** {value}")

        # Display other fields
        for field, value in entity_data.items():
            if field not in priority_fields and value is not None:
                display_name = field.replace("_", " ").title()

                # Skip complex nested objects or lists
                if isinstance(value, (dict, list)) and len(str(value)) > 100:
                    continue

                # Format dates nicely
                if "date" in field.lower() and isinstance(value, str) and "T" in value:
                    try:
                        date_obj = datetime.fromisoformat(value.replace("Z", "+00:00"))
                        value = date_obj.strftime("%B %d, %Y")
                    except Exception:
                        pass

                # Format booleans
                if isinstance(value, bool):
                    value = "Yes" if value else "No"

                output.append(f"**{display_name}:** {value}")

        return "\n".join(output)

    @hr_agent.tool_plain(retries=2)
    async def extract_document_data_tool(
        file_keys: List[str],
        extraction_type: str = "auto",  # auto, text_only, structured, summary
        context_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Extract and process document data from R2 storage

        Args:
            file_keys: List of file keys in R2 storage
            extraction_type: Type of extraction (auto, text_only, structured, summary)
            context_prompt: Optional context for focused extraction

        Returns:
            Dictionary with extracted document data
        """
        return await extract_document_data(
            file_keys=file_keys,
            extraction_type=extraction_type,
            context_prompt=context_prompt,
        )

    @hr_agent.tool_plain(retries=1)
    def create_list_summary(entity_type: str, entities: List[Dict[str, Any]]) -> str:
        """
        Create a summary of multiple entities

        Args:
            entity_type: Type of entities (positions, employees, etc.)
            entities: List of entity data
        """
        if not entities:
            return f"No {entity_type} found."

        summary = f"Found {len(entities)} {entity_type}:\n\n"

        for idx, entity in enumerate(entities, 1):
            # Try to find a good display name
            display_name = (
                entity.get("title")
                or entity.get("name")
                or entity.get("display_name")
                or f"{entity_type} {entity.get('id', 'Unknown')}"
            )

            summary += f"{idx}. **{display_name}**"

            # Add ID if available
            if "id" in entity:
                summary += f" (ID: {entity['id']})"

            summary += "\n"

            # Add some key details
            detail_fields = ["status", "type", "department", "level", "location"]
            details = []

            for field in detail_fields:
                if field in entity and entity[field]:
                    value = entity[field]
                    if isinstance(value, dict) and "name" in value:
                        value = value["name"]
                    details.append(f"{field.title()}: {value}")

            if details:
                summary += f"   - {', '.join(details)}\n"

            summary += "\n"

        return summary

    @hr_agent.tool_plain(retries=1)
    def validate_uuid_format(value: str, field_name: str) -> str:
        """
        Validate UUID format for fields that require it

        Args:
            value: Value to validate
            field_name: Name of the field (for error messages)
        """
        try:
            uuid.UUID(value)
            return value
        except ValueError:
            raise ModelRetry(f"Invalid UUID format for {field_name}: {value}")

    @hr_agent.tool_plain(retries=1)
    def get_current_user_info() -> str:
        """
        Get information about the current user who is making this request.
        Use this instead of creating a new user.
        """
        return f"Current user ID: {user_id}. This user already exists in the system. Use this ID for any CV creation or user-related operations."

    @hr_agent.tool_plain(retries=1)
    def detect_entity_type(tool_name: str, data: Dict[str, Any]) -> str:
        """
        Detect what type of entity we're working with based on tool name and data

        Args:
            tool_name: Name of the tool being used
            data: Data being processed
        """
        # Common patterns in tool names
        if "position" in tool_name.lower():
            return "position"
        elif "employee" in tool_name.lower():
            return "employee"
        elif "department" in tool_name.lower():
            return "department"
        elif "candidate" in tool_name.lower() or "applicant" in tool_name.lower():
            return "candidate"
        elif "interview" in tool_name.lower():
            return "interview"
        elif "offer" in tool_name.lower():
            return "offer"
        else:
            # Try to detect from data fields
            if "job_description" in data or "requirements" in data:
                return "position"
            elif "first_name" in data or "last_name" in data:
                return "employee"
            else:
                return "entity"

    return hr_agent


async def handle_hr_request(
    user,
    message: str,
    chat_history: list,
    model_name: Optional[str] = None,
    session_id: Optional[str] = None,
):
    """Handle HR requests using MCP tools"""

    try:
        user_id = str(getattr(user, "id", user))
        user_role = UserRole(getattr(user, "role", UserRole.ADMIN.value))

        logger.info(
            f"Processing HR request for {user_role.value} user {user_id}: {message}"
        )
        logger.info(f"DEBUG: User object type: {type(user)}")
        logger.info(f"DEBUG: User object attributes: {vars(user) if hasattr(user, '__dict__') else 'No attributes'}")
        logger.info(f"DEBUG: Extracted user_id: {user_id}")
        logger.info(f"DEBUG: Extracted user_role: {user_role}")

        # Validate UUID format
        if not validate_user_id(user_id):
            logger.error(f"User ID {user_id} is NOT valid UUID format")
            yield "Error: Invalid user ID format."
            return

        # Create the HR agent
        agent = create_hr_agent(user_id, user_role)

        # Create dependencies
        deps = HRDependencies(
            user_id=user_id,
            user_role=user_role,
            session_id=session_id,
            context={
                "model_name": model_name,
                "request_timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )

        logger.info(f"HR Dependencies created for {user_role.value} user: {user_id}")

        # Convert chat history
        converted_history = convert_chat_history(chat_history or [])

        try:
            async with agent.run_mcp_servers():
                # Run the HR agent with MCP tools
                result = await agent.run(
                    user_prompt=message,
                    deps=deps,
                    message_history=converted_history
                )

                # Extract the HR result
                hr_result: HRTaskResult = result.output

                # Log task completion
                logger.info("HR task completed:")
                logger.info(f"  Status: {hr_result.task_status}")
                logger.info(f"  Confidence: {hr_result.confidence_level}")
                logger.info(f"  Entities created: {hr_result.entities_created}")

                # Stream the response
                response_text = hr_result.user_response
                yield response_text
                # words = response_text.split()
                # for word in words:
                #    yield word + " "
                #    await asyncio.sleep(0.05)

                logger.info("Successfully streamed HR response")
                
        except* Exception as eg:
            # Handle TaskGroup exceptions properly with except*
            error_messages = []
            for error in eg.exceptions:
                logger.error(f"TaskGroup sub-exception: {type(error).__name__}: {error}")
                error_messages.append(str(error))
            
            error_msg = f"Đã xảy ra lỗi trong quá trình xử lý: {'; '.join(error_messages)}"
            logger.error(f"TaskGroup error: {error_msg}")
            yield error_msg

    except ValidationError as e:
        logger.error(f"HR validation error: {e}")
        yield "I need to gather more information. Please provide all required details."
    except Exception as e:
        logger.error(f"Unexpected error in HR handler: {e}")
        yield f"I encountered an error processing your HR request: {str(e)}"

    yield "\n\ndata: [DONE]\n\n"


# Keep the same conversion function as employee_agent

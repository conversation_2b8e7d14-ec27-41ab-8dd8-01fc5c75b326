import hashlib
import io
import logging
from datetime import datetime, timedelta
from functools import lru_cache
from typing import Any, Dict, List

import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from gentekai.config import settings

logger = logging.getLogger(__name__)


@lru_cache()
def get_r2_client():
    try:
        return boto3.client(
            "s3",
            endpoint_url=settings.UPLOAD_ENDPOINT_URL,
            aws_access_key_id=settings.UPLOAD_ACCESS_KEY_ID,
            aws_secret_access_key=settings.UPLOAD_ACCESS_KEY,
            region_name="auto",
        )
    except NoCredentialsError:
        logger.error("R2 credentials not found")
        raise


class R2StorageService:
    def __init__(self):
        self.client = get_r2_client()
    
    # In your r2_storage_service.py - add this method for debugging
    def upload_file(self, file_key: str, file_content: bytes, content_type: str) -> Dict[str, Any]:
        try:
            # Log what we're about to upload
            content_hash = hashlib.sha256(file_content).hexdigest()[:16]
            logger.info(f"Uploading to R2: {file_key}, {len(file_content)} bytes, "
                    f"type: {content_type}, hash: {content_hash}")
            
            self.client.put_object(
                Bucket=settings.UPLOAD_BUCKET_NAME,
                Key=file_key,
                Body=file_content,
                ContentType=content_type,
            )
            
            logger.info(f"R2 upload successful: {file_key}")
            
            return {
                "file_key": file_key,
                "url": self.get_public_url(file_key),
                "status": "uploaded",
                "size": len(file_content),
                "hash": content_hash
            }
        except ClientError as e:
            logger.error(f"Failed to upload file {file_key}: {e}")
            raise
    
    def upload_multiple_files(
        self, files: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Upload multiple files to R2 storage"""
        results = []
        for file in files:
            try:
                result = self.upload_file(
                    file_key=file["file_key"],
                    file_content=file["content"],
                    content_type=file["content_type"],
                )
                results.append(result)
            except Exception as e:
                logger.error(f"Error uploading file {file['file_key']}: {e}")
                results.append({"file_key": file["file_key"], "error": str(e)})
        return results

    def generate_presigned_url(
        self, file_key: str, content_type: str, file_size: int
    ) -> str:
        return self.client.generate_presigned_url(
            "put_object",
            Params={
                "Bucket": settings.UPLOAD_BUCKET_NAME,
                "Key": file_key,
                "ContentType": content_type,
                "ContentLength": file_size,
            },
            ExpiresIn=settings.PRESIGNED_URL_EXPIRY,
        )

    async def generate_file_urls(self, file_keys: List[str]) -> List[Dict]:
        """Generate presigned URLs for a list of file keys"""
        file_urls = []

        for file_key in file_keys:
            try:
                # Optional: Add file access authorization here
                # if not await can_user_access_file(user_id, file_key):
                #     continue

                presigned_url = self.client.generate_presigned_url(
                    "get_object",
                    Params={
                        "Bucket": settings.UPLOAD_BUCKET_NAME,
                        "Key": file_key,
                    },
                    ExpiresIn=86400,  # 24 hours
                )

                file_urls.append(
                    {
                        "file_key": file_key,
                        "url": presigned_url,
                        "expires_at": (
                            datetime.now() + timedelta(hours=24)
                        ).isoformat(),
                    }
                )

            except ClientError as e:
                logger.error(f"Failed to generate presigned URL for {file_key}: {e}")
                file_urls.append(
                    {
                        "file_key": file_key,
                        "url": None,
                        "error": "Failed to generate URL",
                    }
                )

        return file_urls

    def delete_file(self, file_key: str):
        return self.client.delete_object(
            Bucket=settings.UPLOAD_BUCKET_NAME, Key=file_key
        )

    def get_file_info(self, file_key: str):
        return self.client.head_object(Bucket=settings.UPLOAD_BUCKET_NAME, Key=file_key)

    def file_exists(self, file_key: str) -> bool:
        try:
            self.client.head_object(Bucket=settings.UPLOAD_BUCKET_NAME, Key=file_key)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            raise

    def get_public_url(self, file_key: str) -> str:
        """Get public URL for a file"""
        if settings.UPLOAD_PUBLIC_URL:
            return f"{settings.UPLOAD_PUBLIC_URL}/{file_key}"
        return f"https://{settings.UPLOAD_BUCKET_NAME}.r2.dev/{file_key}"

    def test_bucket_access(self):
        """Test if we can access the bucket"""
        try:
            response = self.client.head_bucket(Bucket=settings.UPLOAD_BUCKET_NAME)
            logger.info(f"Bucket access test successful: {response}")
            return True
        except Exception as e:
            logger.error(f"Bucket access test failed: {e}")
            return False

    def download_file(self, file_key: str) -> bytes:
        """Download file content as bytes"""
        try:
            response = self.client.get_object(
                Bucket=settings.UPLOAD_BUCKET_NAME, Key=file_key
            )
            return response["Body"].read()
        except ClientError as e:
            logger.error(f"Failed to download file {file_key}: {e}")
            raise

    def download_file_stream(self, file_key: str) -> io.BytesIO:
        """Download file content as BytesIO stream"""
        file_content = self.download_file(file_key)
        return io.BytesIO(file_content)

    def get_file_metadata(self, file_key: str) -> Dict[str, Any]:
        """Get comprehensive file metadata"""
        try:
            response = self.client.head_object(
                Bucket=settings.UPLOAD_BUCKET_NAME, Key=file_key
            )
            return {
                "content_type": response.get("ContentType"),
                "content_length": response.get("ContentLength"),
                "last_modified": response.get("LastModified"),
                "etag": response.get("ETag"),
                "metadata": response.get("Metadata", {}),
            }
        except ClientError as e:
            logger.error(f"Failed to get metadata for {file_key}: {e}")
            raise

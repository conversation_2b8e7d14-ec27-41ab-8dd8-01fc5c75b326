import io
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import pymupdf  # pymupdf
import pytesseract
from PIL import Image

logger = logging.getLogger(__name__)


@dataclass
class PDFPage:
    page_number: int
    text: str
    ocr_text: str  # Text extracted via OCR
    combined_text: str  # Regular text + OCR text
    image_count: int
    link_count: int
    has_text: bool  # Whether page has extractable text
    is_scanned: bool  # Whether page appears to be scanned


@dataclass
class PDFDocument:
    filename: str
    page_count: int
    pages: List[PDFPage]
    metadata: Dict[str, Any]
    file_size: int
    has_text_content: bool  # Whether any pages have extractable text
    has_scanned_content: bool  # Whether any pages appear scanned


class PDFReaderOCRService:
    def __init__(self, tesseract_path: Optional[str] = None):
        # Set tesseract path if provided (needed on Windows)
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path

    def _extract_images_from_page(self, page) -> List[Image.Image]:
        """Extract images from a PDF page"""
        images = []
        image_list = page.get_images()

        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = pymupdf.Pixmap(page.parent, xref)

                # Convert to PIL Image
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.pil_tobytes(format="PNG")
                    pil_image = Image.open(io.BytesIO(img_data))
                    images.append(pil_image)
                else:  # CMYK: convert to RGB first
                    pix_rgb = pymupdf.Pixmap(pymupdf.csRGB, pix)
                    img_data = pix_rgb.pil_tobytes(format="PNG")
                    pil_image = Image.open(io.BytesIO(img_data))
                    images.append(pil_image)
                    pix_rgb = None

                pix = None
            except Exception as e:
                logger.warning(f"Failed to extract image {img_index}: {e}")
                continue

        return images

    def _render_page_as_image(self, page, dpi: int = 300) -> Image.Image:
        """Render entire PDF page as image for OCR"""
        try:
            # Render page as pixmap
            mat = pymupdf.Matrix(dpi / 72, dpi / 72)  # Scale factor for DPI
            pix = page.get_pixmap(matrix=mat)

            # Convert to PIL Image
            img_data = pix.pil_tobytes(format="PNG")
            pil_image = Image.open(io.BytesIO(img_data))

            pix = None
            return pil_image
        except Exception as e:
            logger.error(f"Failed to render page as image: {e}")
            return None

    def _perform_ocr(self, image: Image.Image, config: str = "--psm 6") -> str:
        """Perform OCR on an image"""
        try:
            text = pytesseract.image_to_string(image, config=config)
            return text.strip()
        except Exception as e:
            logger.error(f"OCR failed: {e}")
            return ""

    def _is_page_scanned(self, page, text: str) -> bool:
        """Determine if a page is likely scanned (has images but little/no text)"""
        image_count = len(page.get_images())
        text_length = len(text.strip())

        # Heuristic: if page has images but very little extractable text, likely scanned
        if image_count > 0 and text_length < 50:
            return True

        # Check if page is mostly images
        page_area = abs(page.rect)
        if (
            image_count > 0 and text_length < page_area / 1000
        ):  # Very little text relative to page size
            return True

        return False

    def read_pdf_from_bytes(
        self,
        pdf_bytes: bytes,
        filename: str = "",
        use_ocr: bool = True,
        ocr_dpi: int = 300,
    ) -> PDFDocument:
        """Read PDF from bytes and extract content with OCR support"""
        try:
            pdf_stream = io.BytesIO(pdf_bytes)
            doc = pymupdf.open(stream=pdf_stream, filetype="pdf")

            metadata = doc.metadata
            page_count = len(doc)

            pages = []
            has_text_content = False
            has_scanned_content = False

            for page_num in range(page_count):
                page = doc[page_num]

                # Extract regular text
                regular_text = page.get_text()
                has_text = len(regular_text.strip()) > 0
                if has_text:
                    has_text_content = True

                # Determine if page is scanned
                is_scanned = self._is_page_scanned(page, regular_text)
                if is_scanned:
                    has_scanned_content = True

                # Perform OCR if enabled and needed
                ocr_text = ""
                if use_ocr and (is_scanned or not has_text):
                    try:
                        # Render page as image for OCR
                        page_image = self._render_page_as_image(page, dpi=ocr_dpi)
                        if page_image:
                            ocr_text = self._perform_ocr(page_image)

                        # Also OCR individual images if any
                        images = self._extract_images_from_page(page)
                        for img in images:
                            img_ocr = self._perform_ocr(img)
                            if img_ocr:
                                ocr_text += "\n" + img_ocr

                    except Exception as e:
                        logger.warning(f"OCR failed for page {page_num + 1}: {e}")

                # Combine texts
                combined_text = regular_text
                if ocr_text:
                    combined_text += "\n\n[OCR Content]\n" + ocr_text

                # Count images and links
                image_list = page.get_images()
                link_list = page.get_links()

                pdf_page = PDFPage(
                    page_number=page_num + 1,
                    text=regular_text,
                    ocr_text=ocr_text,
                    combined_text=combined_text,
                    image_count=len(image_list),
                    link_count=len(link_list),
                    has_text=has_text,
                    is_scanned=is_scanned,
                )
                pages.append(pdf_page)

            doc.close()

            return PDFDocument(
                filename=filename,
                page_count=page_count,
                pages=pages,
                metadata=metadata,
                file_size=len(pdf_bytes),
                has_text_content=has_text_content,
                has_scanned_content=has_scanned_content,
            )

        except Exception as e:
            logger.error(f"Failed to read PDF {filename}: {e}")
            raise ValueError(f"Invalid PDF file: {e}")

    def extract_text_only(self, pdf_bytes: bytes, use_ocr: bool = True) -> str:
        """Extract text from PDF with OCR support"""
        try:
            pdf_doc = self.read_pdf_from_bytes(pdf_bytes, use_ocr=use_ocr)

            full_text = ""
            for page in pdf_doc.pages:
                # Use combined text (regular + OCR)
                full_text += f"--- Page {page.page_number} ---\n"
                full_text += page.combined_text + "\n\n"

            return full_text.strip()

        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            raise ValueError(f"Invalid PDF file: {e}")

    def get_pdf_page(
        self, pdf_bytes: bytes, page_number: int, use_ocr: bool = True
    ) -> Optional[PDFPage]:
        """Get specific page from PDF with OCR"""
        try:
            pdf_stream = io.BytesIO(pdf_bytes)
            doc = pymupdf.open(stream=pdf_stream, filetype="pdf")

            if page_number < 1 or page_number > len(doc):
                return None

            page = doc[page_number - 1]
            regular_text = page.get_text()
            has_text = len(regular_text.strip()) > 0
            is_scanned = self._is_page_scanned(page, regular_text)

            # Perform OCR if needed
            ocr_text = ""
            if use_ocr and (is_scanned or not has_text):
                try:
                    page_image = self._render_page_as_image(page)
                    if page_image:
                        ocr_text = self._perform_ocr(page_image)
                except Exception as e:
                    logger.warning(f"OCR failed for page {page_number}: {e}")

            combined_text = regular_text
            if ocr_text:
                combined_text += "\n\n[OCR Content]\n" + ocr_text

            image_list = page.get_images()
            link_list = page.get_links()

            pdf_page = PDFPage(
                page_number=page_number,
                text=regular_text,
                ocr_text=ocr_text,
                combined_text=combined_text,
                image_count=len(image_list),
                link_count=len(link_list),
                has_text=has_text,
                is_scanned=is_scanned,
            )

            doc.close()
            return pdf_page

        except Exception as e:
            logger.error(f"Failed to get page {page_number} from PDF: {e}")
            raise ValueError(f"Invalid PDF file: {e}")

    def search_text_in_pdf(
        self, pdf_bytes: bytes, search_term: str, use_ocr: bool = True
    ) -> List[Dict[str, Any]]:
        """Search for text in PDF including OCR content"""
        try:
            pdf_doc = self.read_pdf_from_bytes(pdf_bytes, use_ocr=use_ocr)

            matches = []
            for page in pdf_doc.pages:
                # Search in combined text (regular + OCR)
                text = page.combined_text
                text_lower = text.lower()
                search_lower = search_term.lower()

                if search_lower in text_lower:
                    start = 0
                    while True:
                        pos = text_lower.find(search_lower, start)
                        if pos == -1:
                            break

                        # Get context around the match
                        context_start = max(0, pos - 50)
                        context_end = min(len(text), pos + len(search_term) + 50)
                        context = text[context_start:context_end]

                        # Determine if match was from OCR or regular text
                        source = "text"
                        if pos >= len(page.text) and page.ocr_text:
                            source = "ocr"

                        matches.append(
                            {
                                "page": page.page_number,
                                "position": pos,
                                "context": context,
                                "match": text[pos : pos + len(search_term)],
                                "source": source,
                                "is_scanned_page": page.is_scanned,
                            }
                        )

                        start = pos + 1

            return matches

        except Exception as e:
            logger.error(f"Failed to search in PDF: {e}")
            raise ValueError(f"Invalid PDF file: {e}")

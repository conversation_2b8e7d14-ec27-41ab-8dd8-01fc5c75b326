import logging
from enum import Enum
from typing import Any, Dict, List, Optional

from gentekai.models.document import DocumentType, UniversalDocument
from gentekai.services.embedding_service import EmbeddingService
from gentekai.services.universal_document_service import UniversalDocumentService
from gentekai.services.vision_service import VisionService

logger = logging.getLogger(__name__)


class ContextStrategy(Enum):
    FULL_TEXT = "full_text"  # Include entire document
    EMBEDDINGS = "embeddings"  # Use semantic search
    SUMMARY = "summary"  # Generate summary
    HYBRID = "hybrid"  # Combine approaches


class DocumentContextService:
    def __init__(self):
        self.doc_service = UniversalDocumentService()
        self.embedding_service = EmbeddingService()
        self.vision_service = VisionService()

        # Configure strategies by file type and size
        self.strategy_config = {
            DocumentType.PDF: {
                "small": (ContextStrategy.FULL_TEXT, 40000),  # < 40k chars
                "medium": (ContextStrategy.EMBEDDINGS, 500000),  # < 500k chars
                "large": (ContextStrategy.SUMMARY, float("inf")),
            },
            DocumentType.EXCEL: {
                "small": (ContextStrategy.FULL_TEXT, 80000),
                "medium": (ContextStrategy.HYBRID, 1000000),
                "large": (ContextStrategy.SUMMARY, float("inf")),
            },
            DocumentType.WORD: {
                "small": (ContextStrategy.FULL_TEXT, 40000),
                "medium": (ContextStrategy.EMBEDDINGS, 500000),
                "large": (ContextStrategy.SUMMARY, float("inf")),
            },
            DocumentType.IMAGE: {
                "small": (
                    ContextStrategy.HYBRID,
                    float("inf"),
                ),  # Always hybrid for images
                "medium": (ContextStrategy.HYBRID, float("inf")),
                "large": (ContextStrategy.HYBRID, float("inf")),
            },
        }

    async def prepare_document_context(
        self, file_keys: List[str], user_prompt: str, max_context_length: int = 16000
    ) -> Dict[str, Any]:
        """
        Prepare document context for AI chat based on user prompt and files
        """
        if not file_keys:
            return {"context": "", "files_processed": [], "strategy_used": "none"}

        context_parts = []
        files_processed = []
        total_length = 0

        for file_key in file_keys:
            try:
                # Get document info first
                doc_info = self.doc_service.get_document_info(file_key)
                if not doc_info["supported"]:
                    logger.warning(f"Unsupported file type: {file_key}")
                    continue

                doc_type = DocumentType(doc_info["document_type"])

                # Process document based on type and size
                context_data = await self._process_single_document(
                    file_key, doc_type, user_prompt, max_context_length - total_length
                )

                if context_data:
                    context_parts.append(context_data["context"])
                    files_processed.append(
                        {
                            "file_key": file_key,
                            "strategy": context_data["strategy"],
                            "doc_type": doc_type.value,
                            "context_length": len(context_data["context"]),
                        }
                    )
                    total_length += len(context_data["context"])

                    # Stop if we're approaching context limit
                    if total_length >= max_context_length * 0.9:
                        logger.warning(
                            f"Document {file_key} has {len(context_data['context'])} words "
                        )
                        break

            except Exception as e:
                logger.error(f"Failed to process {file_key}: {e}")
                continue

        # Combine all contexts
        combined_context = self._combine_contexts(context_parts, files_processed)
        logger.info(
            f"Prepared document context with {len(files_processed)} files, total length: {len(combined_context)}"
        )

        return {
            "context": combined_context,
            "files_processed": files_processed,
            "total_context_length": len(combined_context),
            "strategy_used": "mixed"
            if len(set(f["strategy"] for f in files_processed)) > 1
            else files_processed[0]["strategy"]
            if files_processed
            else "none",
        }

    async def _process_single_document(
        self,
        file_key: str,
        doc_type: DocumentType,
        user_prompt: str,
        remaining_context: int,
    ) -> Optional[Dict[str, Any]]:
        """Process a single document based on optimal strategy"""

        try:
            # Get full document first
            document = self.doc_service.process_document_from_r2(file_key)
            content_length = len(document.content)

            # Determine strategy based on size and type
            strategy = self._determine_strategy(doc_type, content_length)
            logger.info(
                f"Document {file_key} has {content_length} words, using strategy: {strategy}"
            )

            if strategy == ContextStrategy.FULL_TEXT:
                return await self._process_full_text(document, remaining_context)

            elif strategy == ContextStrategy.EMBEDDINGS:
                return await self._process_with_embeddings(
                    document, user_prompt, remaining_context
                )

            elif strategy == ContextStrategy.SUMMARY:
                return await self._process_with_summary(
                    document, user_prompt, remaining_context
                )

            elif strategy == ContextStrategy.HYBRID:
                return await self._process_hybrid(
                    document, user_prompt, remaining_context
                )
            return None

        except Exception as e:
            logger.error(f"Error processing document {file_key}: {e}")
            return None

    def _determine_strategy(
        self, doc_type: DocumentType, content_length: int
    ) -> ContextStrategy:
        """Determine the best processing strategy"""
        config = self.strategy_config.get(
            doc_type, self.strategy_config[DocumentType.PDF]
        )

        if content_length < config["small"][1]:
            return config["small"][0]
        elif content_length < config["medium"][1]:
            return config["medium"][0]
        else:
            return config["large"][0]

    async def _process_full_text(
        self, document: UniversalDocument, max_length: int
    ) -> Dict[str, Any]:
        """Include full document text"""
        content = (
            document.content[:max_length]
            if len(document.content) > max_length
            else document.content
        )

        return {
            "context": f"--- Document: {document.filename} ---\n{content}\n",
            "strategy": ContextStrategy.FULL_TEXT.value,
        }

    async def _process_with_embeddings(
        self, document: UniversalDocument, user_prompt: str, max_length: int
    ) -> Dict[str, Any]:
        """Use embedding-based retrieval for relevant content"""
        try:
            # Split document into chunks
            chunks = self._split_document_into_chunks(
                document, chunk_size=500, overlap=50
            )

            # Get embeddings for user prompt
            prompt_embedding = await self.embedding_service.get_embedding(user_prompt)

            # Get embeddings for chunks and find most relevant
            relevant_chunks = await self.embedding_service.find_relevant_chunks(
                chunks, prompt_embedding, top_k=5
            )

            # Combine relevant chunks
            combined_content = "\n\n".join(
                [chunk["content"] for chunk in relevant_chunks]
            )

            # Truncate if needed
            if len(combined_content) > max_length:
                combined_content = combined_content[:max_length]

            context = f"--- Document: {document.filename} (Relevant sections) ---\n{combined_content}\n"

            return {
                "context": context,
                "strategy": ContextStrategy.EMBEDDINGS.value,
                "relevant_chunks": len(relevant_chunks),
            }

        except Exception as e:
            logger.error(f"Embedding processing failed: {e}")
            # Fallback to truncated full text
            return await self._process_full_text(document, max_length)

    async def _process_with_summary(
        self, document: UniversalDocument, user_prompt: str, max_length: int
    ) -> Dict[str, Any]:
        """Generate a summary focused on the user's prompt"""
        try:
            # Use AI to generate a focused summary
            summary = await self._generate_focused_summary(
                document.content, user_prompt, max_length
            )

            return {
                "context": f"--- Document: {document.filename} (Summary) ---\n{summary}\n",
                "strategy": ContextStrategy.SUMMARY.value,
            }

        except Exception as e:
            logger.error(f"Summary generation failed: {e}")
            # Fallback to embeddings
            return await self._process_with_embeddings(
                document, user_prompt, max_length
            )

    async def _process_hybrid(
        self, document: UniversalDocument, user_prompt: str, max_length: int
    ) -> Dict[str, Any]:
        """Hybrid approach - especially good for images and Excel files"""
        if document.file_type == DocumentType.IMAGE:
            # For images: OCR text + vision analysis
            ocr_text = (
                document.content if document.content.strip() else "No text detected"
            )

            # Get vision analysis
            vision_analysis = await self._analyze_image_content(
                document.filename, user_prompt
            )

            context = f"--- Image: {document.filename} ---\n"
            context += f"Text content (OCR): {ocr_text[: max_length // 2]}\n"
            context += f"Visual analysis: {vision_analysis[: max_length // 2]}\n"

        elif document.file_type == DocumentType.EXCEL:
            # For Excel: Summary + relevant sheets based on prompt
            context = f"--- Spreadsheet: {document.filename} ---\n"

            if document.sheets:
                # Find most relevant sheets
                relevant_sheets = self._find_relevant_sheets(
                    document.sheets, user_prompt
                )
                for sheet in relevant_sheets[:2]:  # Limit to 2 most relevant sheets
                    context += f"\nSheet '{sheet.name}':\n"
                    context += self._format_sheet_data(sheet, max_rows=10)

        else:
            # For other documents: Key sections + embeddings
            return await self._process_with_embeddings(
                document, user_prompt, max_length
            )

        return {
            "context": context[:max_length],
            "strategy": ContextStrategy.HYBRID.value,
        }

    def _split_document_into_chunks(
        self, document: UniversalDocument, chunk_size: int = 500, overlap: int = 50
    ) -> List[Dict[str, Any]]:
        """Split document into overlapping chunks for embedding"""
        chunks = []
        content = document.content

        for i in range(0, len(content), chunk_size - overlap):
            chunk_text = content[i : i + chunk_size]
            chunks.append(
                {
                    "content": chunk_text,
                    "start_pos": i,
                    "end_pos": min(i + chunk_size, len(content)),
                    "filename": document.filename,
                }
            )

        return chunks

    async def _analyze_image_content(self, file_key: str, user_prompt: str) -> str:
        """Analyze image content using vision model"""
        try:
            # Download image from R2
            image_bytes = self.doc_service.r2_service.download_file(file_key)

            # Use vision service to analyze image
            analysis = await self.vision_service.analyze_image(
                image_bytes,
                prompt=f"Analyze this image in the context of: {user_prompt}",
            )

            return analysis

        except Exception as e:
            logger.error(f"Vision analysis failed for {file_key}: {e}")
            return "Could not analyze image content"

    def _find_relevant_sheets(self, sheets: List, user_prompt: str) -> List:
        """Find Excel sheets most relevant to user prompt"""
        prompt_words = set(user_prompt.lower().split())

        sheet_scores = []
        for sheet in sheets:
            score = 0
            sheet_text = f"{sheet.name} ".lower()

            # Add header text
            if sheet.headers:
                sheet_text += " ".join(sheet.headers).lower()

            # Add some sample data
            if sheet.data:
                for row in sheet.data[:5]:  # First 5 rows
                    sheet_text += " ".join(str(cell) for cell in row).lower()

            # Calculate relevance score
            for word in prompt_words:
                if word in sheet_text:
                    score += 1

            sheet_scores.append((sheet, score))

        # Sort by relevance score
        sheet_scores.sort(key=lambda x: x[1], reverse=True)
        return [sheet for sheet, score in sheet_scores]

    def _format_sheet_data(self, sheet, max_rows: int = 10) -> str:
        """Format sheet data for context"""
        formatted = ""

        if sheet.headers:
            formatted += " | ".join(sheet.headers) + "\n"
            formatted += "-" * len(" | ".join(sheet.headers)) + "\n"

        for i, row in enumerate(sheet.data[:max_rows]):
            formatted += " | ".join(str(cell) for cell in row) + "\n"

        if len(sheet.data) > max_rows:
            formatted += f"... ({len(sheet.data) - max_rows} more rows)\n"

        return formatted

    def _combine_contexts(
        self, context_parts: List[str], files_processed: List[Dict]
    ) -> str:
        """Combine multiple document contexts"""
        if not context_parts:
            return ""

        combined = "=== DOCUMENT CONTEXT ===\n\n"
        combined += "\n".join(context_parts)
        combined += "\n=== END DOCUMENT CONTEXT ===\n"

        return combined

    async def _generate_focused_summary(
        self, content: str, user_prompt: str, max_length: int
    ) -> str:
        """Generate AI summary focused on user's question"""
        # This would integrate with your AI service to generate summaries
        # For now, return a truncated version with key sections

        # Simple implementation - find paragraphs containing prompt keywords
        prompt_words = set(user_prompt.lower().split())
        paragraphs = content.split("\n\n")

        relevant_paragraphs = []
        for para in paragraphs:
            para_words = set(para.lower().split())
            if prompt_words.intersection(para_words):
                relevant_paragraphs.append(para)

        if relevant_paragraphs:
            summary = "\n\n".join(relevant_paragraphs)
            return summary[:max_length]
        else:
            return content[:max_length]

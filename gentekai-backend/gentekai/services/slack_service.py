# slack_service.py
import logging

from slack_sdk.errors import SlackApiError
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.slack import SlackEventCallbackRequest
from gentekai.db.models.users import User
from gentekai.services.slack.message_processor import MessageProcessor
from gentekai.services.slack.request_tracker import request_tracker
from gentekai.services.slack.slack_client import slack_client
from gentekai.services.slack.slack_config import bot_config

# Initialize services
logger = logging.getLogger(__name__)


async def initialize_bot():
    """Initialize bot configuration"""
    try:
        response = slack_client.auth_test()
        bot_config.user_id = response['user_id']
        logger.info(f"Bot initialized with user ID: {bot_config.user_id}")
    except SlackApiError as e:
        logger.error(f"Failed to initialize bot: {e}")

async def process_slack_event(event_request: SlackEventCallbackRequest, user: User, db: AsyncSession):
    """Main entry point for Slack events - EXACTLY like original"""
    event = event_request.event
    event_id = event_request.event_id
    logger.info(f"Processing Slack event: {event_id}, type: {event.type}")

    if not bot_config.user_id:
        await initialize_bot()

    try:
        status = await request_tracker.get_status()
        logger.info(f"Request tracker status: {status}")

        if event.type == "app_mention":
            logger.info(f"Handling app mention from {event.user}")
            await MessageProcessor.handle_mention(event, user, db)

        elif event.type == "message":
            if (not event.get("bot_id") 
                #and not event.get("subtype") 
                and event.get("text") 
                and event.get("user") 
                and event.user != bot_config.user_id):
                logger.info(f"Processing message from {event.user}")
                await MessageProcessor.process_message(event, user, db)
            else:
                logger.info("Skipping message - bot/subtype/no text")

        else:
            logger.info(f"Ignoring event type: {event.type}")

    except Exception as e:
        logger.error(f"Error processing event {event_id}: {e}", exc_info=True)

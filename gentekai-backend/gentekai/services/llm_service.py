from typing import Dict, List

from openai import OpenAI

from gentekai.config import settings

OPENROUTER_API_KEY = settings.OPENROUTER_API_KEY
SYSTEM_PROMPT = (
    "You are a helpful, conversational AI assistant. "
    "Have friendly, open-ended conversations about any topic. "
)

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=OPENROUTER_API_KEY,
)


def stream_chat_completion_raw(
    messages: List[Dict[str, str]],
    referer: str = "<YOUR_SITE_URL>",
    model: str | None = None,
    system_prompt: str | None = None,
    title: str = "<YOUR_SITE_NAME>",
):
    if model is None:
        model = settings.GENERAL_LLM_MODEL
    """
    Low-level wrapper around OpenRouter chat streaming.
    Yields chunks of data as text.
    """

    if system_prompt is None:
        system_prompt = SYSTEM_PROMPT
        # compose messages with system prompt:

    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "system", "content": system_prompt}] + messages,
        stream=True,
        extra_headers={
            "HTTP-Referer": referer,
            "X-Title": title,
        },
    )
    for chunk in response:
        delta = chunk.choices[0].delta.content
        if delta:
            yield delta


async def get_chat_completion_raw(
    messages: List[Dict[str, str]],
    referer: str = "<YOUR_SITE_URL>",
    model: str | None = None,
    system_prompt: str | None = None,
    title: str = "<YOUR_SITE_NAME>",
):
    if model is None:
        model = settings.GENERAL_LLM_MODEL
    """
    Low-level wrapper around OpenRouter chat streaming but with async await.
    """

    if system_prompt is None:
        system_prompt = SYSTEM_PROMPT
        # compose messages with system prompt:

    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "system", "content": system_prompt}] + messages,
        stream=False,  # Get complete response at once
        extra_headers={
            "HTTP-Referer": referer,
            "X-Title": title,
        },
    )

    full_response = response.choices[0].message.content
    return full_response


def call_llm_with_tools(
    messages,
    tools,
    system_prompt,
    stream=False,
    model: str | None = None,
):
    if model is None:
        model = settings.GENERAL_LLM_MODEL
    params = {
        "model": model,  # or your model
        "messages": [{"role": "system", "content": system_prompt}] + messages,
        "tools": tools,
        "tool_choice": "auto",
        "stream": stream,
    }
    if stream:
        return client.chat.completions.create(**params)
    else:
        response = client.chat.completions.create(**params)
        return response

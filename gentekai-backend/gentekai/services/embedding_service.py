import logging
from typing import Any, Dict, List

import numpy as np
from openai import AsyncOpenAI

from gentekai.config import settings

logger = logging.getLogger(__name__)


class EmbeddingService:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "text-embedding-3-small"

    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for a text"""
        try:
            response = await self.client.embeddings.create(input=text, model=self.model)
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            return []

    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for multiple texts"""
        try:
            response = await self.client.embeddings.create(
                input=texts, model=self.model
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            logger.error(f"Failed to get batch embeddings: {e}")
            return []

    async def find_relevant_chunks(
        self, chunks: List[Dict[str, Any]], query_embedding: List[float], top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """Find most relevant chunks using cosine similarity"""
        if not query_embedding:
            return chunks[:top_k]  # Fallback to first chunks

        try:
            # Get embeddings for all chunks
            chunk_texts = [chunk["content"] for chunk in chunks]
            chunk_embeddings = await self.get_embeddings_batch(chunk_texts)

            if not chunk_embeddings:
                return chunks[:top_k]

            # Calculate similarities
            similarities = []
            query_embedding = np.array(query_embedding)

            for i, chunk_embedding in enumerate(chunk_embeddings):
                chunk_emb = np.array(chunk_embedding)
                similarity = np.dot(query_embedding, chunk_emb) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(chunk_emb)
                )
                similarities.append((i, similarity))

            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x[1], reverse=True)

            relevant_chunks = []
            for i, similarity in similarities[:top_k]:
                chunk = chunks[i].copy()
                chunk["similarity"] = similarity
                relevant_chunks.append(chunk)

            return relevant_chunks

        except Exception as e:
            logger.error(f"Failed to find relevant chunks: {e}")
            return chunks[:top_k]

import logging
from typing import Any, Dict, List, Optional

from gentekai.models.document import UniversalDocument
from gentekai.services.document_factory import DocumentProcessorFactory
from gentekai.storage.r2 import R2StorageService

logger = logging.getLogger(__name__)


class UniversalDocumentService:
    def __init__(self, tesseract_path: Optional[str] = None):
        self.r2_service = R2StorageService()
        self.processor_factory = DocumentProcessorFactory(tesseract_path)

    def process_document_from_r2(self, file_key: str, **kwargs) -> UniversalDocument:
        """Process any supported document from R2"""
        if not self.r2_service.file_exists(file_key):
            raise ValueError(f"File {file_key} doesn't exist")

        try:
            # Get file metadata
            metadata = self.r2_service.get_file_metadata(file_key)
            content_type = metadata.get("content_type")

            # Get appropriate processor
            processor = self.processor_factory.get_processor(file_key, content_type)
            if not processor:
                raise ValueError(f"Unsupported file type: {file_key}")

            # Download and process
            file_bytes = self.r2_service.download_file(file_key)
            document = processor.process_document(file_bytes, file_key, **kwargs)

            return document

        except Exception as e:
            logger.error(f"Failed to process document {file_key}: {e}")
            raise

    def extract_text_from_r2(self, file_key: str, **kwargs) -> str:
        """Extract text from any supported document in R2"""
        try:
            metadata = self.r2_service.get_file_metadata(file_key)
            content_type = metadata.get("content_type")

            processor = self.processor_factory.get_processor(file_key, content_type)
            if not processor:
                raise ValueError(f"Unsupported file type: {file_key}")

            file_bytes = self.r2_service.download_file(file_key)
            return processor.extract_text_only(file_bytes, **kwargs)

        except Exception as e:
            logger.error(f"Failed to extract text from {file_key}: {e}")
            raise

    def search_in_document(
        self, file_key: str, search_term: str, **kwargs
    ) -> List[Dict[str, Any]]:
        """Search for text in any supported document"""
        try:
            document = self.process_document_from_r2(file_key, **kwargs)

            processor = self.processor_factory.get_processor(file_key)
            if processor:
                return processor.search_content(document, search_term)
            else:
                return []

        except Exception as e:
            logger.error(f"Failed to search in document {file_key}: {e}")
            raise

    def get_document_info(self, file_key: str) -> Dict[str, Any]:
        """Get information about a document without full processing"""
        try:
            metadata = self.r2_service.get_file_metadata(file_key)
            content_type = metadata.get("content_type")

            doc_type = self.processor_factory.detect_document_type(
                file_key, content_type
            )
            processor = self.processor_factory.get_processor(file_key, content_type)

            return {
                "filename": file_key,
                "document_type": doc_type.value,
                "file_size": metadata.get("content_length", 0),
                "content_type": content_type,
                "last_modified": metadata.get("last_modified"),
                "supported": processor is not None,
                "processor_type": processor.__class__.__name__ if processor else None,
            }

        except Exception as e:
            logger.error(f"Failed to get document info for {file_key}: {e}")
            raise

    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get all supported file formats organized by document type"""
        formats = {}
        for doc_type, processor in self.processor_factory.processors.items():
            formats[doc_type.value] = processor.supported_types
        return formats

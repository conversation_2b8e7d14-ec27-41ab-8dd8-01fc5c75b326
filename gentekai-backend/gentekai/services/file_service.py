from fastapi import HTTPException, status

from gentekai.config import settings


class FileValidationService:
    def __init__(self):
        pass

    def validate_file(self, filename: str, content_type: str, file_size: int) -> str:
        """Validate file and return extension"""

        # Check content type
        if content_type not in settings.ALLOWED_CONTENT_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type '{content_type}' not allowed",
            )

        # Check file extension
        file_extension = None
        if "." in filename:
            file_extension = "." + filename.split(".")[-1].lower()
            if file_extension not in settings.ALLOWED_EXTENSIONS:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File extension '{file_extension}' not allowed",
                )

        # Check file size
        if file_size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File size too large. Maximum allowed: {settings.MAX_FILE_SIZE} bytes",
            )

        return file_extension or ""

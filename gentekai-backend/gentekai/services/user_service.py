# ============================================================================
# 📄 app/services/user_service.py - User Service
# ============================================================================
import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.db.models import User
from gentekai.db.models.users import UserRole
from gentekai.db.repositories.user_repository import UserRepository

logger = logging.getLogger(__name__)


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.user_repo = UserRepository(db)

    def _validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return re.match(pattern, email) is not None

    def _clean_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and validate user data"""
        cleaned_data = {}

        # Required fields
        if "email" in user_data:
            email = user_data["email"].lower().strip()
            if not self._validate_email(email):
                raise ValueError(f"Invalid email format: {email}")
            cleaned_data["email"] = email

        if "clerk_user_id" in user_data:
            clerk_id = str(user_data["clerk_user_id"]).strip()
            if not clerk_id:
                raise ValueError("clerk_user_id cannot be empty")
            cleaned_data["clerk_user_id"] = clerk_id

        # Optional fields
        if "first_name" in user_data and user_data["first_name"]:
            cleaned_data["first_name"] = str(user_data["first_name"]).strip()[:100]

        if "last_name" in user_data and user_data["last_name"]:
            cleaned_data["last_name"] = str(user_data["last_name"]).strip()[:100]

        # Role handling
        if "role" in user_data:
            role_value = user_data["role"]
            if isinstance(role_value, str):
                try:
                    cleaned_data["role"] = UserRole(role_value)
                except ValueError:
                    logger.warning(
                        f"Invalid role value: {role_value}, defaulting to USER"
                    )
                    cleaned_data["role"] = UserRole.USER
            elif isinstance(role_value, UserRole):
                cleaned_data["role"] = role_value
        else:
            cleaned_data["role"] = UserRole.USER

        # Profile data (JSONB)
        if "profile" in user_data and isinstance(user_data["profile"], dict):
            cleaned_data["profile"] = user_data["profile"]

        return cleaned_data

    async def create_user_from_clerk(
        self, clerk_data: Dict[str, Any]
    ) -> Tuple[User, bool]:
        """Create user from Clerk webhook data"""
        try:
            # Extract user data from Clerk payload
            user_data = self._extract_user_data_from_clerk(clerk_data)

            # Clean and validate
            cleaned_data = self._clean_user_data(user_data)

            # Check if user already exists
            existing_user = await self.user_repo.get_by_clerk_id(
                cleaned_data["clerk_user_id"]
            )
            if existing_user:
                logger.info(
                    f"User with clerk_id {cleaned_data['clerk_user_id']} already exists"
                )
                return existing_user, False

            # Create new user
            new_user = await self.user_repo.create_user(cleaned_data)
            logger.info(
                f"Created new user: {new_user.id} from Clerk ID: {cleaned_data['clerk_user_id']}"
            )

            return new_user, True

        except Exception as e:
            logger.error(f"Failed to create user from Clerk data: {e}")
            raise

    def _extract_user_data_from_clerk(
        self, clerk_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract user data from Clerk webhook payload"""
        data = clerk_data.get("data", {})

        # Get primary email
        email_addresses = data.get("email_addresses", [])
        primary_email = None

        for email_obj in email_addresses:
            if email_obj.get("id") == data.get("primary_email_address_id"):
                primary_email = email_obj.get("email_address")
                break

        if not primary_email and email_addresses:
            # Fallback to first email if no primary found
            primary_email = email_addresses[0].get("email_address")

        if not primary_email:
            raise ValueError("No email address found in Clerk data")

        # Extract profile data
        profile_data = {
            "clerk_created_at": data.get("created_at"),
            "clerk_updated_at": data.get("updated_at"),
            "image_url": data.get("image_url"),
            "has_image": data.get("has_image", False),
            "external_accounts": data.get("external_accounts", []),
            "public_metadata": data.get("public_metadata", {}),
            "private_metadata": data.get("private_metadata", {}),
            "unsafe_metadata": data.get("unsafe_metadata", {}),
        }

        return {
            "clerk_user_id": data.get("id"),
            "email": primary_email,
            "first_name": data.get("first_name"),
            "last_name": data.get("last_name"),
            "profile": profile_data,
        }

    async def update_user_from_clerk(
        self, clerk_data: Dict[str, Any]
    ) -> Optional[User]:
        """Update user from Clerk webhook data"""
        try:
            # Extract user data
            user_data = self._extract_user_data_from_clerk(clerk_data)
            clerk_user_id = user_data["clerk_user_id"]

            # Clean data (excluding clerk_user_id as it shouldn't change)
            update_data = self._clean_user_data(
                {k: v for k, v in user_data.items() if k != "clerk_user_id"}
            )

            # Update user
            updated_user = await self.user_repo.update_by_clerk_id(
                clerk_user_id, update_data
            )

            if updated_user:
                logger.info(
                    f"Updated user: {updated_user.id} from Clerk ID: {clerk_user_id}"
                )
            else:
                logger.warning(
                    f"User with Clerk ID {clerk_user_id} not found for update"
                )

            return updated_user

        except Exception as e:
            logger.error(f"Failed to update user from Clerk data: {e}")
            raise

    async def delete_user_from_clerk(self, clerk_user_id: str) -> bool:
        """Delete user from Clerk webhook data"""
        try:
            success = await self.user_repo.delete_by_clerk_id(clerk_user_id)

            if success:
                logger.info(f"Deleted user with Clerk ID: {clerk_user_id}")
            else:
                logger.warning(
                    f"User with Clerk ID {clerk_user_id} not found for deletion"
                )

            return success

        except Exception as e:
            logger.error(f"Failed to delete user with Clerk ID {clerk_user_id}: {e}")
            raise

    async def get_user_by_clerk_id(self, clerk_user_id: str) -> Optional[User]:
        """Get user by Clerk ID"""
        return await self.user_repo.get_by_clerk_id(clerk_user_id)

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by internal ID"""
        return await self.user_repo.get_by_id(user_id)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return await self.user_repo.get_by_email(email)

    async def update_user(
        self, user_id: str, update_data: Dict[str, Any]
    ) -> Optional[User]:
        """Update user by internal ID"""
        try:
            cleaned_data = self._clean_user_data(update_data)
            return await self.user_repo.update_user(user_id, cleaned_data)
        except Exception as e:
            logger.error(f"Failed to update user {user_id}: {e}")
            raise

    async def search_users(
        self, query: str, limit: int = 20, offset: int = 0
    ) -> Tuple[List[User], int]:
        """Search users"""
        if not query or len(query.strip()) < 2:
            raise ValueError("Search query must be at least 2 characters long")

        return await self.user_repo.search_users(query.strip(), limit, offset)

    async def get_users_by_role(
        self, role: UserRole, limit: int = 50, offset: int = 0
    ) -> Tuple[List[User], int]:
        """Get users by role"""
        return await self.user_repo.get_users_by_role(role, limit, offset)

    async def promote_user_to_admin(self, user_id: str) -> Optional[User]:
        """Promote user to admin role"""
        return await self.user_repo.update_user(user_id, {"role": UserRole.ADMIN})

    async def demote_admin_to_user(self, user_id: str) -> Optional[User]:
        """Demote admin to user role"""
        return await self.user_repo.update_user(user_id, {"role": UserRole.USER})

    async def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics"""
        return await self.user_repo.get_user_stats()

    async def sync_user_with_clerk(
        self, clerk_user_id: str, clerk_data: Dict[str, Any]
    ) -> User:
        """Sync user with Clerk data (create or update)"""
        try:
            existing_user = await self.user_repo.get_by_clerk_id(clerk_user_id)

            if existing_user:
                # Update existing user
                updated_user = await self.update_user_from_clerk(clerk_data)
                return updated_user or existing_user
            else:
                # Create new user
                new_user, created = await self.create_user_from_clerk(clerk_data)
                return new_user

        except Exception as e:
            logger.error(f"Failed to sync user with Clerk ID {clerk_user_id}: {e}")
            raise

    # ============================================================================
    # HEALTH CHECK
    # ============================================================================
    async def health_check(self) -> Dict[str, Any]:
        """Health check for user service"""
        try:
            stats = await self.get_user_stats()

            return {
                "service": "user_service",
                "status": "healthy",
                "database": "connected",
                "total_users": stats.get("total_users", 0),
                "admin_users": stats.get("admin_count", 0),
                "regular_users": stats.get("user_count", 0),
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"User service health check failed: {e}")
            return {
                "service": "user_service",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, <PERSON><PERSON>

from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from gentekai.db.models import Conversation
from gentekai.db.repositories.conversation_repository import ConversationRepository
from gentekai.db.repositories.message_repository import MessageRepository
from gentekai.db.repositories.user_repository import UserRepository

logger = logging.getLogger(__name__)


class ConversationService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.conversation_repo = ConversationRepository(db)
        self.message_repo = MessageRepository(db)
        self.user_repo = UserRepository(db)

    async def create_conversation(
        self,
        user_id: str,
        title: Optional[str] = None,
        initial_message: Optional[str] = None,
        slack_thread_id: Optional[str] = None,
    ) -> Conversation:
        """Create a new conversation"""

        conversation_data = {
            "user_id": user_id,
            "title": title or "New Conversation",
            "slack_thread_id": slack_thread_id,
        }

        conversation = await self.conversation_repo.create(conversation_data)

        # Add initial message if provided
        if initial_message:
            await self.message_repo.create_message(
                conversation_id=str(conversation.id),
                sender="user",
                content=initial_message,
            )

        return conversation

    async def list_user_conversations(
        self,
        user_id: str,
        limit: int = 20,
        offset: int = 0,
        search: Optional[str] = None,
        order_by: str = "updated_at",
        order_dir: str = "desc",
    ) -> Tuple[List[Conversation], int]:
        self._validate_pagination(limit, offset)

        # Get conversations using the enhanced method (or fall back to original)
        try:
            (
                conversations,
                total,
            ) = await self.conversation_repo.get_by_user_id_with_stats(
                user_id=user_id,
                limit=limit,
                offset=offset,
                search=search,
                order_by=order_by,
                order_dir=order_dir,
            )
        except AttributeError:
            # Fallback if get_by_user_id_with_stats doesn't exist
            logger.warning(
                "Using fallback method - consider implementing get_by_user_id_with_stats"
            )
            conversations, total = await self.conversation_repo.get_by_user_id(
                user_id=user_id,
                limit=limit,
                offset=offset,
                search=search,
                order_by=order_by,
                order_dir=order_dir,
            )

        if not conversations:
            return [], total

        conversation_ids = [str(conv.id) for conv in conversations]

        # Batch fetch recent messages and stats to avoid N+1 problem
        try:
            # Try to use batch methods
            recent_messages_batch = (
                await self.message_repo.get_recent_by_conversations_batch(
                    conversation_ids
                )
            )
            stats_batch = await self.conversation_repo.get_conversations_stats_batch(
                conversation_ids
            )
        except AttributeError:
            # Fallback to individual queries if batch methods don't exist
            logger.warning(
                "Batch methods not implemented - using individual queries (N+1 problem)"
            )
            recent_messages_batch = {}
            stats_batch = {}

            for conv_id in conversation_ids:
                try:
                    # Get recent message
                    recent_messages = (
                        await self.message_repo.get_recent_by_conversation(
                            conv_id, limit=1
                        )
                    )
                    if recent_messages:
                        recent_messages_batch[conv_id] = recent_messages[0]

                    # Get stats
                    stats = await self.conversation_repo.get_conversation_stats(conv_id)
                    stats_batch[conv_id] = stats
                except Exception as e:
                    logger.error(f"Failed to get data for conversation {conv_id}: {e}")
                    stats_batch[conv_id] = {"message_count": 0}

        # Enhance conversations with computed fields
        enhanced_conversations = []
        for conv in conversations:
            conv_id = str(conv.id)
            recent_msg = recent_messages_batch.get(conv_id)
            stats = stats_batch.get(conv_id, {})

            # Add computed fields to the conversation object
            conv.message_count = stats.get("message_count", 0)
            conv.last_message_at = recent_msg.created_at if recent_msg else None

            # Handle message preview
            if recent_msg and recent_msg.content:
                if len(recent_msg.content) > 100:
                    conv.last_message_preview = recent_msg.content[:100] + "..."
                else:
                    conv.last_message_preview = recent_msg.content
            else:
                conv.last_message_preview = None

            # Add archive status (if field exists)
            conv.is_archived = getattr(conv, "is_archived", False)

            enhanced_conversations.append(conv)

        return enhanced_conversations, total

    async def get_conversation_by_id(self, conversation_id: str, user_id: str):
        """Get a specific conversation"""

        return await self.conversation_repo.get_with_messages(
            conversation_id=conversation_id, user_id=user_id
        )

    async def get_conversation_id_from_thread_ts(
        self, thread_ts: str, user_id: str = None
    ) -> Optional[str]:
        """Get conversation ID from Slack thread timestamp"""

        return await self.conversation_repo.get_by_slack_thread_id(
            slack_thread_id=thread_ts, user_id=user_id
        )

    async def update_conversation(
        self, conversation_id: str, user_id: str, update_data: Dict[str, Any]
    ):
        """Update conversation"""

        # Verify ownership first
        conversation = await self.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            return None

        return await self.conversation_repo.update(conversation_id, update_data)

    async def delete_conversation(
        self, conversation_id: str, user_id: str, permanent: bool = False
    ) -> bool:
        """Delete a conversation"""

        # Verify ownership first
        conversation = await self.get_conversation_by_id(conversation_id, user_id)
        if not conversation:
            return False

        if permanent:
            # Hard delete - this will cascade to messages due to FK constraint
            return await self.conversation_repo.delete(conversation_id)
        else:
            # Soft delete - check if fields exist
            try:
                update_data = {"deleted_at": datetime.utcnow(), "is_deleted": True}
                updated = await self.conversation_repo.update(
                    conversation_id, update_data
                )
                return updated is not None
            except Exception as e:
                logger.warning(f"Soft delete failed, falling back to hard delete: {e}")
                return await self.conversation_repo.delete(conversation_id)

    async def get_conversation_messages(
        self, conversation_id: str, limit: int = 50, offset: int = 0, order: str = "asc"
    ):
        """Get messages for a conversation"""

        return await self.message_repo.get_by_conversation(
            conversation_id=conversation_id, limit=limit, offset=offset, order=order
        )

    async def get_conversation_analytics(self, conversation_id: str):
        """Get analytics for a conversation"""

        try:
            return await self.message_repo.get_conversation_analytics(conversation_id)
        except AttributeError:
            # Fallback implementation if method doesn't exist
            logger.warning("get_conversation_analytics not implemented, using fallback")

            # Get basic stats using existing methods
            try:
                await self.conversation_repo.get_conversation_stats(conversation_id)
                messages = await self.message_repo.get_by_conversation(
                    conversation_id=conversation_id,
                    limit=1000,  # Get all messages for analytics
                )

                # Calculate analytics
                user_messages = [m for m in messages if m.sender == "user"]
                assistant_messages = [m for m in messages if m.sender == "assistant"]

                total_chars = sum(len(m.content) for m in messages)
                avg_length = total_chars / len(messages) if messages else 0

                return {
                    "conversation_id": conversation_id,
                    "total_messages": len(messages),
                    "user_messages": len(user_messages),
                    "assistant_messages": len(assistant_messages),
                    "first_message_at": messages[0].created_at if messages else None,
                    "last_message_at": messages[-1].created_at if messages else None,
                    "average_message_length": round(avg_length, 2),
                    "total_characters": total_chars,
                }
            except Exception as e:
                logger.error(f"Failed to generate analytics fallback: {e}")
                return {
                    "conversation_id": conversation_id,
                    "total_messages": 0,
                    "user_messages": 0,
                    "assistant_messages": 0,
                    "first_message_at": None,
                    "last_message_at": None,
                    "average_message_length": 0.0,
                    "total_characters": 0,
                }

    async def bulk_archive_conversations(
        self, conversation_ids: List[str], user_id: str
    ):
        """Bulk archive conversations"""

        successful = []
        failed = []

        for conv_id in conversation_ids:
            try:
                # Verify ownership
                conversation = await self.get_conversation_by_id(conv_id, user_id)
                if not conversation:
                    failed.append(
                        {"id": conv_id, "error": "Not found or access denied"}
                    )
                    continue

                # Try to archive
                try:
                    update_data = {
                        "archived_at": datetime.utcnow(),
                        "is_archived": True,
                    }
                    await self.conversation_repo.update(conv_id, update_data)
                    successful.append(conv_id)
                except Exception:
                    # Archive fields might not exist
                    logger.warning(
                        f"Archive fields not implemented for conversation {conv_id}"
                    )
                    failed.append({"id": conv_id, "error": "Archive not supported"})

            except Exception as e:
                failed.append({"id": conv_id, "error": str(e)})

        return {"archived": successful, "failed": failed}

    async def bulk_delete_conversations(
        self, conversation_ids: List[str], user_id: str, permanent: bool = False
    ):
        """Bulk delete conversations"""

        successful = []
        failed = []

        for conv_id in conversation_ids:
            try:
                success = await self.delete_conversation(conv_id, user_id, permanent)
                if success:
                    successful.append(conv_id)
                else:
                    failed.append(
                        {"id": conv_id, "error": "Not found or access denied"}
                    )
            except Exception as e:
                failed.append({"id": conv_id, "error": str(e)})

        return {"deleted": successful, "failed": failed}

    # ============================================================================
    # HEALTH CHECK
    # ============================================================================

    async def health_check(self) -> Dict[str, Any]:
        try:
            # Test database connection with conversations count
            stmt = select(func.count(Conversation.id))
            result = await self.db.execute(stmt)
            conversation_count = result.scalar()

            # Also get message count
            from gentekai.db.models import Message

            msg_stmt = select(func.count(Message.id))
            msg_result = await self.db.execute(msg_stmt)
            message_count = msg_result.scalar()

            return {
                "service": "conversation_service",
                "status": "healthy",
                "database": "connected",
                "total_conversations": conversation_count,
                "total_messages": message_count,
                "timestamp": datetime.utcnow().isoformat(),
            }
        except Exception as e:
            logger.error(f"Conversation service health check failed: {e}")
            return {
                "service": "conversation_service",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    def _validate_pagination(self, limit: int, offset: int):
        if limit < 1 or limit > 100:
            raise ValueError("Limit must be between 1 and 100")
        if offset < 0:
            raise ValueError("Offset must be non-negative")

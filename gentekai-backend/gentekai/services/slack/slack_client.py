import logging
from typing import Dict, List, Optional

from slack_sdk import Web<PERSON><PERSON>
from slack_sdk.errors import SlackApiError

from gentekai.config import settings

from .request_tracker import bot_config

logger = logging.getLogger(__name__)


slack_client = WebClient(token=settings.SLACK_BOT_TOKEN)

class SlackClientHelpers:
    """Helper methods for Slack operations"""
    
    @staticmethod
    async def get_user_name(user_id: str) -> str:
        """Get user's display name"""
        try:
            response = slack_client.users_info(user=user_id)
            profile = response["user"].get("profile", {})
            return profile.get("display_name") or profile.get("real_name") or user_id
        except Exception as e:
            logger.error(f"Error getting user name: {e}")
            return user_id

    @staticmethod
    async def get_slack_user_profile(user_id: str) -> Dict:
        """Get user's profile information"""
        try:
            response = slack_client.users_info(user=user_id)
            logger.info(f"Fetched slack profile for user {user_id}: {response}")
            return response.get("user", {}).get("profile", {})
        except SlackApiError as e:
            logger.error(f"Error getting user profile: {e}")
            return {}

    @staticmethod
    async def get_conversation_context(channel: str, thread_ts: Optional[str] = None) -> List[Dict]:
        """Get recent conversation messages"""
        try:
            if thread_ts:
                response = slack_client.conversations_replies(
                    channel=channel, ts=thread_ts, limit=bot_config.max_context_messages
                )
            else:
                response = slack_client.conversations_history(
                    channel=channel, limit=bot_config.max_context_messages
                )

            messages = []
            for msg in response.get("messages", []):
                if (msg.get("bot_id") or msg.get("subtype") == "bot_message" or 
                    (bot_config.user_id and msg.get("user") == bot_config.user_id)):
                    messages.append({
                        "user": bot_config.bot_name,
                        "text": msg.get("text", ""),
                        "ts": msg["ts"],
                    })
                elif msg.get("text"):
                    messages.append({
                        "user": msg.get("user", "unknown"),
                        "text": msg.get("text", ""),
                        "ts": msg["ts"],
                    })

            # Add user names
            user_ids = list(set(msg["user"] for msg in messages if msg["user"] != "unknown"))
            user_names = {}
            for user_id in user_ids:
                user_names[user_id] = await SlackClientHelpers.get_user_name(user_id)

            for msg in messages:
                msg["name"] = user_names.get(msg["user"], msg["user"])

            messages.sort(key=lambda x: float(x["ts"]))
            return messages

        except SlackApiError as e:
            logger.error(f"Error getting conversation: {e}")
            return []


import logging
from slack_sdk import WebClient
from gentekai.api.schemas.slack import SlackEventCallbackRequest
from gentekai.config import settings
from gentekai.db.models.users import User
from gentekai.services.chat_service import ChatService
from gentekai.services.conversation_service import ConversationService
from gentekai.services.slack.message_processor import ConversationContextService
from gentekai.services.slack.help_decision_service import HelpDecisionService
from gentekai.services.slack.request_tracker import RequestTracker
from gentekai.services.slack.slack_client import SlackClientWrapper
from gentekai.services.slack.slack_config import SlackBotConfig
from gentekai.services.slack.slack_service import SlackService
from sqlalchemy.ext.asyncio import AsyncSession


logger = logging.getLogger(__name__)

class SlackServiceContainer:
    """Simple dependency injection container for Slack services"""
    
    def __init__(self):
        self._config = None
        self._slack_client = None
        self._request_tracker = None
        self._context_service = None
        self._help_decision_service = None
        self._slack_service = None

    def config(self) -> SlackBotConfig:
        if self._config is None:
            self._config = SlackBotConfig(
                slack_bot_token=settings.SLACK_BOT_TOKEN,
                help_threshold=0.7,
                max_context_messages=15,
                bot_name="Assistant",
                max_concurrent_requests=3,
                request_timeout=30.0
            )
            # Initialize bot user ID
            client = WebClient(token=self._config.slack_bot_token)
            try:
                response = client.auth_test()
                self._config.user_id = response["user_id"]
            except Exception as e:
                logger.error(f"Failed to initialize bot user ID: {e}")
        return self._config

    def slack_client(self) -> SlackClientWrapper:
        if self._slack_client is None:
            config = self.config()
            client = WebClient(token=config.slack_bot_token)
            self._slack_client = SlackClientWrapper(client, config.bot_name)
        return self._slack_client

    def request_tracker(self) -> RequestTracker:
        if self._request_tracker is None:
            config = self.config()
            self._request_tracker = RequestTracker(
                max_concurrent=config.max_concurrent_requests,
                timeout=config.request_timeout,
                cooldown=config.cooldown_period
            )
        return self._request_tracker

    def context_service(self) -> ConversationContextService:
        if self._context_service is None:
            config = self.config()
            self._context_service = ConversationContextService(
                self.slack_client(),
                config.user_id,
                config.bot_name
            )
        return self._context_service

    def help_decision_service(self) -> HelpDecisionService:
        if self._help_decision_service is None:
            # Note: ChatService needs to be created per request with db session
            # So we pass a factory function
            self._help_decision_service = HelpDecisionService(
                chat_service=None,  # Will be injected per request
                help_threshold=self.config().help_threshold
            )
        return self._help_decision_service

    def slack_service(self) -> SlackService:
        if self._slack_service is None:
            self._slack_service = SlackService(
                slack_client=self.slack_client(),
                request_tracker=self.request_tracker(),
                context_service=self.context_service(),
                help_decision_service=self.help_decision_service(),
                chat_service_factory=lambda db: ChatService(db),
                conversation_service_factory=lambda db: ConversationService(db),
                config=self.config()
            )
        return self._slack_service

# Global container instance
container = SlackServiceContainer()

# Main entry point
async def process_slack_event(event_request: SlackEventCallbackRequest, user: User, db: AsyncSession):
    """Main entry point for Slack events"""
    slack_service = container.slack_service()
    await slack_service.process_event(event_request, user, db)

# Expose the container for dependency injection
def get_slack_service() -> SlackService:
    return container.slack_service()


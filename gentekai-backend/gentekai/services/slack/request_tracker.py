import asyncio
import logging
import time
from typing import Dict

from gentekai.services.slack.slack_config import bot_config

logger = logging.getLogger(__name__)



class RequestTracker:
    """Track active requests to prevent overload"""
    
    def __init__(self):
        self.active_requests: Dict[str, float] = {}
        self.completed_recently: Dict[str, float] = {}
        self._lock = asyncio.Lock()

    async def can_handle_request(self, channel_id: str) -> bool:
        """Check if we can handle a new request for this channel"""
        async with self._lock:
            current_time = time.time()
            self._cleanup_old_entries(current_time)

            if channel_id in self.active_requests:
                elapsed = current_time - self.active_requests[channel_id]
                if elapsed < bot_config.request_timeout:
                    logger.info(f"Already processing request for channel {channel_id}")
                    return False

            if channel_id in self.completed_recently:
                elapsed = current_time - self.completed_recently[channel_id]
                if elapsed < 2.0:
                    logger.info(f"Cooling down for channel {channel_id}")
                    return False

            if len(self.active_requests) >= bot_config.max_concurrent_requests:
                logger.warning(f"Max concurrent requests reached ({bot_config.max_concurrent_requests})")
                return False

            return True

    async def start_request(self, channel_id: str) -> bool:
        """Mark a request as started"""
        async with self._lock:
            current_time = time.time()

            if channel_id in self.active_requests:
                return False

            if channel_id in self.completed_recently:
                if current_time - self.completed_recently[channel_id] < 2.0:
                    return False

            if len(self.active_requests) >= bot_config.max_concurrent_requests:
                return False

            self.active_requests[channel_id] = current_time
            logger.info(f"Started request for channel {channel_id}")
            return True

    async def end_request(self, channel_id: str):
        """Mark a request as completed"""
        async with self._lock:
            if channel_id in self.active_requests:
                del self.active_requests[channel_id]
                self.completed_recently[channel_id] = time.time()
                logger.info(f"Completed request for channel {channel_id}")

    def _cleanup_old_entries(self, current_time: float):
        """Remove old entries - called while holding lock"""
        timeout_channels = [
            ch for ch, start_time in self.active_requests.items()
            if current_time - start_time > bot_config.request_timeout
        ]
        for ch in timeout_channels:
            logger.warning(f"Request timeout for channel {ch}")
            del self.active_requests[ch]

        old_completed = [
            ch for ch, end_time in self.completed_recently.items()
            if current_time - end_time > 60
        ]
        for ch in old_completed:
            del self.completed_recently[ch]

    async def get_status(self) -> Dict:
        """Get current tracker status"""
        async with self._lock:
            return {
                "active_requests": len(self.active_requests),
                "active_channels": list(self.active_requests.keys()),
                "recently_completed": len(self.completed_recently),
                "can_accept_more": len(self.active_requests) < bot_config.max_concurrent_requests,
            }
        
request_tracker = RequestTracker()
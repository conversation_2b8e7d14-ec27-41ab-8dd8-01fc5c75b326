from dataclasses import dataclass
from typing import Optional


@dataclass
class SlackBotConfig:
    """Centralized bot configuration"""

    user_id: Optional[str] = None
    help_threshold: float = 0.7
    max_context_messages: int = 15
    bot_name: str = "Assistant"
    max_concurrent_requests: int = 3
    request_timeout: float = 30.0
    cooldown_period: float = 2.0

# Global instances
bot_config = SlackBotConfig()
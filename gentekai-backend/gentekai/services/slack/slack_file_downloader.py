import asyncio
import httpx
from io import Bytes<PERSON>
from typing import Optional, Dict, Any
import logging
import json

logger = logging.getLogger(__name__)

class SlackFileDownloader:
    """Async file downloader for Slack with proper authentication handling."""
    
    def __init__(self, token: str, timeout: int = 30):
        """
        Initialize the Slack file downloader.
        
        Args:
            token: Slack API token (should start with xoxb- for bot tokens)
            timeout: Request timeout in seconds
        """
        self.token = token
        self.timeout = httpx.Timeout(timeout)
        self.client: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            follow_redirects=True,
            headers={
                "User-Agent": "SlackFileDownloader/1.0"
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.client:
            await self.client.aclose()
            
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create an httpx async client."""
        if not self.client:
            self.client = httpx.AsyncClient(
                timeout=self.timeout,
                follow_redirects=True,
                headers={
                    "User-Agent": "SlackFileDownloader/1.0"
                }
            )
        return self.client
        
    async def download_file(
        self, 
        file_url: str, 
        expected_size: Optional[int] = None, 
        file_name: str = "", 
        max_retries: int = 3
    ) -> Optional[BytesIO]:
        """
        Download a file from Slack with retry logic.
        
        Args:
            file_url: The URL of the file to download
            expected_size: Expected file size in bytes (for validation)
            file_name: File name for logging purposes
            max_retries: Maximum number of retry attempts
            
        Returns:
            BytesIO object containing the file data, or None if download fails
        """
        client = await self._get_client()
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting to download {file_name or file_url} (attempt {attempt + 1}/{max_retries})")
                
                # Add exponential backoff for retries
                if attempt > 0:
                    wait_time = 2 ** attempt
                    logger.info(f"Waiting {wait_time} seconds before retry...")
                    await asyncio.sleep(wait_time)
                
                # Add Authorization header to the request
                headers = {
                    "Authorization": f"Bearer {self.token}"
                }
                
                # First, do a HEAD request to check the file
                try:
                    head_response = await client.head(file_url, headers=headers)
                    content_type = head_response.headers.get('content-type', '')
                    
                    # Check if we're getting an HTML page (likely an error)
                    if 'text/html' in content_type:
                        logger.error(f"URL returns HTML instead of file content. This usually means authentication failed or URL is invalid.")
                        
                        # Try to download anyway to see the error
                        error_response = await client.get(file_url, headers=headers)
                        error_content = error_response.text[:500]  # First 500 chars
                        logger.error(f"HTML response preview: {error_content}")
                        
                        # If it's a Slack auth error, don't retry
                        if 'slack.com/signin' in error_content or 'unauthorized' in error_content.lower():
                            logger.error("Authentication failed. Check your Slack token permissions.")
                            return None
                            
                except Exception as e:
                    logger.warning(f"HEAD request failed: {e}, continuing with GET")
                
                # Stream the download
                async with client.stream('GET', file_url, headers=headers) as response:
                    # Check response status
                    response.raise_for_status()
                    
                    # Check content type
                    content_type = response.headers.get('content-type', '')
                    if 'text/html' in content_type:
                        # Read a bit of the response to log
                        preview = await response.aread()
                        preview_text = preview[:500].decode('utf-8', errors='ignore')
                        logger.error(f"Received HTML instead of file. Preview: {preview_text}")
                        
                        # Check for common Slack error patterns
                        if 'Error - Slack' in preview_text:
                            logger.error("Slack returned an error page")
                            return None
                        elif 'Sign in to Slack' in preview_text:
                            logger.error("Authentication required - token may be invalid")
                            return None
                            
                    # Get content length
                    content_length = response.headers.get('Content-Length')
                    if content_length:
                        content_length = int(content_length)
                        logger.info(f"File size from header: {content_length:,} bytes")
                        
                        # Validate expected size if provided
                        if expected_size and content_length != expected_size:
                            logger.warning(
                                f"Size mismatch in header: expected {expected_size:,} bytes, "
                                f"got {content_length:,} bytes"
                            )
                            
                            # If the actual size is much smaller, it might be an error page
                            if content_length < expected_size * 0.1:  # Less than 10% of expected
                                logger.error("File size is suspiciously small, might be an error page")
                    
                    # Download file content
                    chunks = []
                    downloaded = 0
                    chunk_size = 8192
                    
                    async for chunk in response.aiter_bytes(chunk_size):
                        chunks.append(chunk)
                        downloaded += len(chunk)
                        
                        # Log progress for large files
                        if content_length and content_length > 1_000_000:  # > 1MB
                            progress = (downloaded / content_length) * 100
                            if downloaded % (content_length // 10) < chunk_size:  # Log every 10%
                                logger.debug(f"Download progress: {progress:.1f}%")
                    
                    # Combine chunks
                    file_data = b''.join(chunks)
                    actual_size = len(file_data)
                    
                    # Check if the content looks like HTML/error page
                    if actual_size < 100000:  # If less than 100KB, check content
                        try:
                            text_preview = file_data[:1000].decode('utf-8', errors='ignore')
                            if '<html' in text_preview.lower() or '<!doctype' in text_preview.lower():
                                logger.error(f"Downloaded content appears to be HTML: {text_preview[:200]}...")
                                return None
                        except:
                            pass  # Binary file, continue
                    
                    # Final size validation
                    if expected_size and actual_size != expected_size:
                        logger.error(
                            f"Downloaded size mismatch: expected {expected_size:,} bytes, "
                            f"got {actual_size:,} bytes"
                        )
                        
                        # If size is way off, probably an error
                        if actual_size < expected_size * 0.5:  # Less than 50% of expected
                            logger.error("Downloaded file is much smaller than expected")
                            return None
                    
                    # Create BytesIO object
                    file_buffer = BytesIO(file_data)
                    file_buffer.seek(0)
                    
                    logger.info(f"Successfully downloaded {file_name or file_url} ({actual_size:,} bytes)")
                    return file_buffer
                    
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    logger.error("401 Unauthorized - Check your Slack token")
                    return None  # Don't retry auth errors
                elif e.response.status_code == 403:
                    logger.error("403 Forbidden - Token may lack required scopes")
                    return None
                elif e.response.status_code == 404:
                    logger.error(f"404 File not found: {file_name or file_url}")
                    return None
                else:
                    logger.error(
                        f"HTTP error {e.response.status_code}: {e.response.text[:200]} "
                        f"(attempt {attempt + 1}/{max_retries})"
                    )
                    
            except Exception as e:
                logger.error(f"Error downloading {file_name or file_url}: {e} (attempt {attempt + 1}/{max_retries})")
                
        logger.error(f"Failed to download {file_name or file_url} after {max_retries} attempts")
        return None
        
    async def verify_slack_token(self) -> bool:
        """
        Verify that the Slack token is valid and has necessary permissions.
        
        Returns:
            True if token is valid, False otherwise
        """
        client = await self._get_client()
        
        try:
            # Test the token with auth.test endpoint
            response = await client.post(
                "https://slack.com/api/auth.test",
                headers={"Authorization": f"Bearer {self.token}"}
            )
            data = response.json()
            
            if data.get('ok'):
                logger.info(f"Token validated for team: {data.get('team')} as user: {data.get('user')}")
                return True
            else:
                logger.error(f"Token validation failed: {data.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return False
            
    async def get_file_info(self, file_id: str) -> Optional[Dict]:
        """
        Get file information from Slack API before downloading.
        
        Args:
            file_id: Slack file ID (starts with F)
            
        Returns:
            File info dict or None if failed
        """
        client = await self._get_client()
        
        try:
            response = await client.get(
                "https://slack.com/api/files.info",
                params={"file": file_id},
                headers={"Authorization": f"Bearer {self.token}"}
            )
            data = response.json()
            
            if data.get('ok'):
                return data.get('file')
            else:
                logger.error(f"Failed to get file info: {data.get('error')}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            return None


# Example usage with debugging:
async def debug_slack_download():
    async with SlackFileDownloader(token="xoxb-your-token") as downloader:
        # First verify the token
        if not await downloader.verify_slack_token():
            logger.error("Invalid Slack token!")
            return
            
        # Get file info first (if you have the file ID)
        file_id = "F1234567890"  # Your Slack file ID
        file_info = await downloader.get_file_info(file_id)
        if file_info:
            logger.info(f"File info: {json.dumps(file_info, indent=2)}")
            
        # Try to download
        file_url = "https://files.slack.com/files-pri/..."
        result = await downloader.download_file(
            file_url=file_url,
            expected_size=685638,
            file_name="test_file.pdf"
        )
        
        if result:
            logger.info(f"Download successful: {result.getbuffer().nbytes} bytes")
        else:
            logger.error("Download failed")

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    asyncio.run(debug_slack_download())
# services/help_decision_service.py
import json
import logging
from typing import Dict, List

from gentekai.services.chat_service import ChatService

logger = logging.getLogger(__name__)

class HelpDecisionService:
    """Service for LLM-based conversation analysis"""
    
    @staticmethod
    async def should_bot_help(messages: List[Dict]) -> Dict:
        """Ask LLM if bot should help with the conversation"""
        if not messages:
            return {"should_help": False, "confidence": 0, "reason": "No messages"}

        conversation = "\n".join([
            f"{msg['name']}: {msg['text']}"
            for msg in messages[-10:]
        ])

        prompt = f"""You are analyzing a Slack conversation to determine if an AI assistant should offer help.

CONVERSATION:
{conversation}

IMPORTANT GUIDELINES:
- Only suggest helping if users are CLEARLY struggling or asking questions
- Look for: direct questions, confusion, technical problems, requests for help
- DO NOT help with: casual chat, jokes, personal conversations, resolved issues
- Be conservative - when in doubt, don't interrupt
- Only response in JSON format

Analyze and respond with <PERSON><PERSON><PERSON> a JSON object, no other text:
{{
    "should_help": true/false,
    "confidence": 0.0-1.0,
    "reason": "one sentence explanation"
}}

RESPOND WITH ONLY THE JSON OBJECT AND NOTHING ELSE."""

        try:
            chat_service = ChatService()
            response_text = await chat_service.get_completion(prompt)
            logger.info(f"LLM response: {response_text}")
            return json.loads(response_text)

        except json.JSONDecodeError as e:
            logger.error(f"JSON parse error: {e}")
            return {"should_help": False, "confidence": 0, "reason": "Failed to parse LLM response"}
        except Exception as e:
            logger.error(f"LLM analysis error: {e}")
            return {"should_help": False, "confidence": 0, "reason": f"Analysis error: {str(e)}"}

    """Service for determining when the bot should offer help"""
    
    def __init__(self, chat_service, help_threshold: float = 0.7):
        self.chat_service = chat_service
        self.help_threshold = help_threshold

    async def should_offer_help(self, messages: List[Dict]) -> Dict:
        """Determine if bot should offer help based on conversation"""
        if not messages:
            return {"should_help": False, "confidence": 0, "reason": "No messages"}

        try:
            conversation = self._format_conversation(messages)
            prompt = self._build_analysis_prompt(conversation)
            
            response_text = await self.chat_service.get_completion(prompt)
            analysis = json.loads(response_text)
            
            logger.info(f"Help analysis: {analysis}")
            
            should_help = (
                analysis.get("should_help", False) and 
                analysis.get("confidence", 0) >= self.help_threshold
            )
            
            return {
                **analysis,
                "will_help": should_help
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parse error in help analysis: {e}")
            return {"should_help": False, "confidence": 0, "reason": "Parse error"}
        except Exception as e:
            logger.error(f"Error in help analysis: {e}")
            return {"should_help": False, "confidence": 0, "reason": f"Analysis error: {str(e)}"}

    def _format_conversation(self, messages: List[Dict]) -> str:
        """Format messages into conversation string"""
        return "\n".join([
            f"{msg['name']}: {msg['text']}"
            for msg in messages[-10:]  # Last 10 messages for context
        ])

    def _build_analysis_prompt(self, conversation: str) -> str:
        """Build prompt for LLM analysis"""
        return f"""You are analyzing a Slack conversation to determine if an AI assistant should offer help.

CONVERSATION:
{conversation}

IMPORTANT GUIDELINES:
- Only suggest helping if users are CLEARLY struggling or asking questions
- Look for: direct questions, confusion, technical problems, requests for help
- DO NOT help with: casual chat, jokes, personal conversations, resolved issues
- Be conservative - when in doubt, don't interrupt
- Only response in JSON format

Analyze and respond with ONLY a JSON object, no other text:
{{
    "should_help": true/false,
    "confidence": 0.0-1.0,
    "reason": "one sentence explanation"
}}

RESPOND WITH ONLY THE JSON OBJECT AND NOTHING ELSE."""
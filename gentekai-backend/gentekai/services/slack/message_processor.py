import logging
from typing import List

from slack_sdk.errors import SlackApiError
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.slack import Slack<PERSON>vent
from gentekai.db.models.users import User
from gentekai.services.chat_service import ChatService
from gentekai.services.conversation_service import ConversationService
from gentekai.services.slack.help_decision_service import HelpDecisionService
from gentekai.services.slack.request_tracker import request_tracker
from gentekai.services.slack.slack_client import SlackClientHel<PERSON>, slack_client
from gentekai.services.slack.slack_config import bot_config
from gentekai.services.slack.slack_file_handler import SlackFileHandler

logger = logging.getLogger(__name__)

# In your slack_service.py - Updated MessageProcessor class
file_handler = SlackFileHandler(slack_client,)

class MessageProcessor:
    @staticmethod
    async def direct_help_response_with_files(
        message: str, 
        conv_id: str, 
        user: User, 
        db: AsyncSession,
        file_keys: List[str] = None,
        document_context: str = "",
    ) -> str:
        """Generate direct help response with file context support"""
        try:
            # Enhanced prompt with document context (similar to your existing pattern)
            enhanced_prompt = message
            if document_context:
                enhanced_prompt = f"User uploaded documents with these context: {document_context}\n\n\n\nUser Question: {message}\n\nPlease answer based on the provided document context above."
            logger.info(f"Generating response for message: {enhanced_prompt} with conv_id: {conv_id}")
            response_str = ""
            chat_service = ChatService(db)
            async for chunk in chat_service.stream_chat_with_agents(
                prompt=message,
                conv_id=conv_id,
                user=user,
                enhanced_prompt=enhanced_prompt,
                file_keys=file_keys or [],
                document_context=document_context,
            ):
                if chunk != "\n\ndata: [DONE]\n\n":
                    response_str += chunk
            return response_str.strip()
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I encountered an error while processing your request. Please try again."

    @staticmethod
    async def handle_mention(event: SlackEvent, user: User, db: AsyncSession):
        """Enhanced handle_mention with file upload and context analysis"""
        channel_id = event.channel

        if not await request_tracker.start_request(channel_id):
            logger.info(f"Skipping message - bot busy with channel {channel_id}")
            return
        # Check if message contains files
        has_files = event.files and len(event.files) > 0
        
        slack_thread_ts = event.thread_ts
        conv_id = ""
        new_message_ts = None
        
        # Initialize conversation
        if slack_thread_ts is None:
            conversation_starter_message = slack_client.chat_postMessage(
                channel=channel_id,
                thread_ts=event.ts,
                text="🤔 Processing...",
            )
            new_message_ts = conversation_starter_message["ts"]
            slack_thread_ts = conversation_starter_message["message"]["thread_ts"]
            
            conversation_service = ConversationService(db)
            new_conversation = await conversation_service.create_conversation(
                user_id=user.id,
                # Use the thread timestamp as the conversation ID
                title= f"Direct Message at {slack_thread_ts}",
                initial_message=event.text,
                slack_thread_id=slack_thread_ts,
            )
            conv_id = new_conversation.id
            logger.info(f"Created new conversation {conv_id} for direct message {channel_id}")
        else:
            conversation_service = ConversationService(db)
            conv = await conversation_service.get_conversation_id_from_thread_ts(
                slack_thread_ts, user_id=user.id
            )
            conv_id = conv.id if conv else ""
            logger.info(f"Found conversation {conv_id} for direct message {channel_id}")

        # Process files if present
        file_keys = []
        document_context = ""
        
        if has_files:
            try:
                # Convert SlackFile objects to dict format for processing
                files_data = []
                for file in event.files:
                    files_data.append({
                        'name': file.name,
                        'filetype': file.filetype,
                        'mimetype': file.mimetype,
                        'size': file.size,
                        'url_private_download': file.url_private_download,
                    })
                
                # Process files with R2 upload and context analysis
                file_result = await file_handler.process_file_upload_with_context(
                    files=files_data,
                    user_message=event.text,
                    max_context_length=8000  # Adjust as needed
                )

                logger.info(f"File processing result: {file_result}")
                if not file_result:
                    logger.warning("No files processed successfully.")
                    return
                
                file_keys = file_result["file_keys"]
                document_context = file_result["document_context"]
               
                
                # Update the processing message with file summary
                # if new_message_ts:
                #     slack_client.chat_update(
                #         channel=channel_id,
                #         ts=new_message_ts,
                #         text=f"📁 {file_processing_summary}\n\n🤔 Analyzing your question...",
                #     )
                
            except Exception as e:
                logger.error(f"Error processing files: {e}")
            

        # Check if this is a CV upload request
        is_cv_upload = (
            has_files and
            document_context and
            any(keyword in event.text.lower() for keyword in ['add cv', 'cv to system', 'cv mới', 'thêm cv', 'upload cv'])
        )

        # Generate response with file context
        try:
            if is_cv_upload:
                # Use specialized CV upload agent
                from gentekai.agent.specialized.cv_upload_agent import handle_cv_upload_request
                logger.info("Detected CV upload request, using CV upload agent")

                response_text = ""
                async for chunk in handle_cv_upload_request(
                    user=user,
                    message=event.text,
                    chat_history=[],
                    document_context=document_context,
                ):
                    response_text += chunk
            else:
                # Use regular chat service
                response_text = await MessageProcessor.direct_help_response_with_files(
                    message=event.text,
                    conv_id=conv_id,
                    user=user,
                    db=db,
                    file_keys=file_keys,
                    document_context=document_context,
                )
            
           
            final_response = response_text
                
        except Exception as e:
            logger.error(f"Error generating chat response: {e}")
            final_response = "I encountered an error while processing your request. Please try again."

        # Send final response
        try:
            if new_message_ts:
                slack_client.chat_update(
                    channel=channel_id,
                    ts=new_message_ts,
                    text=final_response,
                )
            else:
                slack_client.chat_postMessage(
                    channel=channel_id,
                    thread_ts=event.thread_ts or event.ts,
                    text=final_response,
                )
            logger.info(f"Sent help response to {channel_id}")
        except SlackApiError as e:
            logger.error(f"Error sending help: {e}")
        finally:
            await request_tracker.end_request(channel_id)

    @staticmethod
    async def process_message(event: SlackEvent, user: User, db: AsyncSession):
        """Process a regular message with file support"""
        channel_id = event.channel

        try:
            if event.channel_type == "im":
                await MessageProcessor.handle_mention(event=event, user=user, db=db)
                return
                
            messages = await SlackClientHelpers.get_conversation_context(channel_id, event.thread_ts)
            logger.info(f"Processing message from {event.user} in {channel_id}")
            logger.info(f"Got {len(messages)} messages for context")

            if not messages:
                logger.info("No messages found, skipping")
                return

            # Check if the latest message has files - might trigger more proactive help
            has_files = event.files and len(event.files) > 0
            
            analysis = await HelpDecisionService.should_bot_help(messages)

            # Boost confidence if files are present (files often indicate need for help)
            if has_files and analysis.get("confidence", 0) > 0.5:
                analysis["confidence"] = min(1.0, analysis["confidence"] + 0.2)
                logger.info(f"Boosted confidence due to file upload: {analysis['confidence']}")

            logger.info(
                f"Help analysis: should_help={analysis.get('should_help', False)}, "
                f"confidence={analysis.get('confidence', 0)}, "
                f"reason={analysis.get('reason', 'none')}"
            )

            if (analysis.get("should_help", False) and 
                analysis.get("confidence", 0) >= bot_config.help_threshold):
                
                await MessageProcessor.handle_mention(event=event, user=user, db=db)

        except Exception as e:
            logger.error(f"Error processing message: {e}", exc_info=True)
        finally:
            await request_tracker.end_request(channel_id)
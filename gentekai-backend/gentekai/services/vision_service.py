import base64
import logging

from openai import AsyncOpenAI

from gentekai.config import settings

logger = logging.getLogger(__name__)


class VisionService:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "gpt-4.1-mini"

    async def analyze_image(
        self, image_bytes: bytes, prompt: str = "Describe this image"
    ) -> str:
        """Analyze image content using vision model"""
        try:
            # Convert image to base64
            base64_image = base64.b64encode(image_bytes).decode("utf-8")

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                ],
                max_tokens=500,
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Vision analysis failed: {e}")
            return f"Could not analyze image: {str(e)}"

    async def extract_text_from_image(self, image_bytes: bytes) -> str:
        """Extract text from image using vision model"""
        return await self.analyze_image(
            image_bytes,
            "Extract and transcribe all text visible in this image. If no text is visible, say 'No text found'.",
        )

    async def describe_charts_and_diagrams(self, image_bytes: bytes) -> str:
        """Describe charts, diagrams, and visual data"""
        return await self.analyze_image(
            image_bytes,
            "Describe any charts, graphs, diagrams, or visual data in this image. Include data points, trends, and key information.",
        )

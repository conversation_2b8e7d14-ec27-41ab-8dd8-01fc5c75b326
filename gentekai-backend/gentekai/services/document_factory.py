from typing import Dict, List, Optional

from gentekai.models.document import DocumentType
from gentekai.services.processors.base_processor import BaseDocumentProcessor
from gentekai.services.processors.excel_processor import ExcelProcessor
from gentekai.services.processors.image_processor import ImageProcessor
from gentekai.services.processors.pdf_processor import PDFProcessor
from gentekai.services.processors.word_processor import WordProcessor


class DocumentProcessorFactory:
    def __init__(self, tesseract_path: Optional[str] = None):
        self.processors: Dict[DocumentType, BaseDocumentProcessor] = {
            DocumentType.PDF: PDFProcessor(tesseract_path),
            DocumentType.EXCEL: ExcelProcessor(),
            DocumentType.WORD: WordProcessor(),
            DocumentType.IMAGE: ImageProcessor(tesseract_path),
        }

    def get_processor(
        self, file_key: str, content_type: str = None
    ) -> Optional[BaseDocumentProcessor]:
        """Get the appropriate processor for a file"""
        for processor in self.processors.values():
            if processor.can_process(file_key, content_type):
                return processor
        return None

    def get_supported_extensions(self) -> List[str]:
        """Get all supported file extensions"""
        extensions = []
        for processor in self.processors.values():
            extensions.extend(processor.supported_types)
        return extensions

    def add_processor(
        self, document_type: DocumentType, processor: BaseDocumentProcessor
    ):
        """Add or replace a processor"""
        self.processors[document_type] = processor

    def detect_document_type(
        self, file_key: str, content_type: str = None
    ) -> DocumentType:
        """Detect document type from file extension or content type"""
        processor = self.get_processor(file_key, content_type)
        return processor.document_type if processor else DocumentType.UNKNOWN

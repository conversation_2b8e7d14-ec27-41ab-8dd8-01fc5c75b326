import io
from typing import List, Optional

import pytesseract
from PIL import Image

from gentekai.models.document import (
    ContentSource,
    DocumentPage,
    DocumentType,
    UniversalDocument,
)
from gentekai.services.processors.base_processor import BaseDocumentProcessor


class ImageProcessor(BaseDocumentProcessor):
    def __init__(self, tesseract_path: Optional[str] = None):
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path

    @property
    def supported_types(self) -> List[str]:
        return [".png", ".jpg", ".jpeg", ".tiff", ".tif", ".bmp", ".gif"]

    @property
    def document_type(self) -> DocumentType:
        return DocumentType.IMAGE

    def can_process(self, file_key: str, content_type: str = None) -> bool:
        return any(file_key.lower().endswith(ext) for ext in self.supported_types) or (
            content_type and content_type.startswith("image/")
        )

    def process_document(
        self, file_bytes: bytes, filename: str, **kwargs
    ) -> UniversalDocument:
        ocr_config = kwargs.get("ocr_config", "--psm 6")
        preprocess = kwargs.get("preprocess", True)

        try:
            image = Image.open(io.BytesIO(file_bytes))

            # Preprocess image for better OCR if requested
            if preprocess:
                image = self._preprocess_image(image)

            # Perform OCR
            ocr_text = pytesseract.image_to_string(image, config=ocr_config)

            # Get image metadata
            metadata = {
                "format": image.format,
                "mode": image.mode,
                "size": image.size,
                "width": image.width,
                "height": image.height,
            }

            # Add EXIF data if available
            if hasattr(image, "_getexif") and image._getexif():
                metadata["exif"] = dict(image._getexif())

            page = DocumentPage(
                page_number=1,
                content=ocr_text,
                source=ContentSource.OCR,
                metadata=metadata,
            )

            return UniversalDocument(
                filename=filename,
                file_type=DocumentType.IMAGE,
                content=ocr_text,
                pages=[page],
                metadata=metadata,
                file_size=len(file_bytes),
                has_text_content=False,
                has_scanned_content=True,
                processing_info={"ocr_config": ocr_config, "preprocessed": preprocess},
            )

        except Exception as e:
            raise ValueError(f"Cannot process image: {e}")

    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR results"""
        try:
            # Convert to grayscale if not already
            if image.mode != "L":
                image = image.convert("L")

            # Resize if too small (OCR works better on larger images)
            width, height = image.size
            if width < 300 or height < 300:
                scale_factor = max(300 / width, 300 / height)
                new_size = (int(width * scale_factor), int(height * scale_factor))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            return image
        except Exception:
            return image  # Return original if preprocessing fails

    def extract_text_only(self, file_bytes: bytes, **kwargs) -> str:
        try:
            image = Image.open(io.BytesIO(file_bytes))
            ocr_config = kwargs.get("ocr_config", "--psm 6")

            if kwargs.get("preprocess", True):
                image = self._preprocess_image(image)

            return pytesseract.image_to_string(image, config=ocr_config)
        except Exception as e:
            return f"Error extracting text from image: {e}"

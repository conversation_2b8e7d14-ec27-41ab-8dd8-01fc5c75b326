from abc import ABC, abstractmethod
from typing import Any, Dict, List

from gentekai.models.document import DocumentType, UniversalDocument


class BaseDocumentProcessor(ABC):
    """Base class for all document processors"""

    @property
    @abstractmethod
    def supported_types(self) -> List[str]:
        """Return list of supported file extensions"""
        pass

    @property
    @abstractmethod
    def document_type(self) -> DocumentType:
        """Return the document type this processor handles"""
        pass

    @abstractmethod
    def can_process(self, file_key: str, content_type: str = None) -> bool:
        """Check if this processor can handle the file"""
        pass

    @abstractmethod
    def process_document(
        self, file_bytes: bytes, filename: str, **kwargs
    ) -> UniversalDocument:
        """Process the document and return UniversalDocument"""
        pass

    @abstractmethod
    def extract_text_only(self, file_bytes: bytes, **kwargs) -> str:
        """Fast text-only extraction"""
        pass

    def search_content(
        self, document: UniversalDocument, search_term: str
    ) -> List[Dict[str, Any]]:
        """Default search implementation"""
        matches = []
        search_lower = search_term.lower()

        # Search in main content
        if document.content and search_lower in document.content.lower():
            # Find positions and context
            content_lower = document.content.lower()
            start = 0
            while True:
                pos = content_lower.find(search_lower, start)
                if pos == -1:
                    break

                context_start = max(0, pos - 50)
                context_end = min(len(document.content), pos + len(search_term) + 50)
                context = document.content[context_start:context_end]

                matches.append(
                    {
                        "location": "document",
                        "position": pos,
                        "context": context,
                        "match": document.content[pos : pos + len(search_term)],
                    }
                )
                start = pos + 1

        # Search in pages if available
        if document.pages:
            for page in document.pages:
                if search_lower in page.content.lower():
                    matches.append(
                        {
                            "location": f"page_{page.page_number}",
                            "page": page.page_number,
                            "source": page.source.value,
                            "context": page.content[:100] + "..."
                            if len(page.content) > 100
                            else page.content,
                        }
                    )

        # Search in sheets if available
        if document.sheets:
            for sheet in document.sheets:
                for row_idx, row in enumerate(sheet.data):
                    for col_idx, cell in enumerate(row):
                        if search_lower in str(cell).lower():
                            matches.append(
                                {
                                    "location": f"sheet_{sheet.name}",
                                    "sheet": sheet.name,
                                    "row": row_idx,
                                    "column": col_idx,
                                    "cell_value": cell,
                                }
                            )

        return matches

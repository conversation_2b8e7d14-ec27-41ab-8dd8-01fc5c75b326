import io
from typing import List

import pandas as pd
from openpyxl import load_workbook

from gentekai.models.document import DocumentSheet, DocumentType, UniversalDocument
from gentekai.services.processors.base_processor import BaseDocumentProcessor


class ExcelProcessor(BaseDocumentProcessor):
    @property
    def supported_types(self) -> List[str]:
        return [".xlsx", ".xls", ".xlsm"]

    @property
    def document_type(self) -> DocumentType:
        return DocumentType.EXCEL

    def can_process(self, file_key: str, content_type: str = None) -> bool:
        return any(file_key.lower().endswith(ext) for ext in self.supported_types) or (
            content_type
            and any(t in content_type.lower() for t in ["excel", "spreadsheet"])
        )

    def process_document(
        self, file_bytes: bytes, filename: str, **kwargs
    ) -> UniversalDocument:
        include_formulas = kwargs.get("include_formulas", True)
        max_rows = kwargs.get("max_rows", None)

        try:
            # Use openpyxl for .xlsx files to get formulas
            if filename.lower().endswith(".xlsx"):
                return self._process_with_openpyxl(
                    file_bytes, filename, include_formulas, max_rows
                )
            else:
                return self._process_with_pandas(file_bytes, filename, max_rows)
        except Exception:
            # Fallback to pandas
            return self._process_with_pandas(file_bytes, filename, max_rows)

    def _process_with_openpyxl(
        self, file_bytes: bytes, filename: str, include_formulas: bool, max_rows: int
    ) -> UniversalDocument:
        file_stream = io.BytesIO(file_bytes)
        workbook = load_workbook(file_stream, data_only=False)

        sheets = []
        full_content = ""

        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]

            # Get data with formulas if requested
            data = []
            headers = []

            # Get headers from first row
            first_row = next(
                worksheet.iter_rows(min_row=1, max_row=1, values_only=True), None
            )
            if first_row:
                headers = [str(cell) if cell is not None else "" for cell in first_row]

            # Get data rows
            start_row = 2 if headers else 1
            end_row = max_rows + 1 if max_rows else worksheet.max_row + 1

            for row in worksheet.iter_rows(min_row=start_row, max_row=end_row):
                row_data = []
                for cell in row:
                    if include_formulas and cell.data_type == "f":
                        # Include formula
                        cell_value = f"={cell.value}" if cell.value else ""
                    else:
                        cell_value = str(cell.value) if cell.value is not None else ""
                    row_data.append(cell_value)

                if any(cell.strip() for cell in row_data):  # Skip empty rows
                    data.append(row_data)

            # Create sheet object
            sheet = DocumentSheet(
                name=sheet_name,
                data=data,
                headers=headers,
                metadata={
                    "max_row": worksheet.max_row,
                    "max_column": worksheet.max_column,
                    "has_formulas": include_formulas,
                },
            )
            sheets.append(sheet)

            # Add to full content
            full_content += f"\n--- Sheet: {sheet_name} ---\n"
            if headers:
                full_content += " | ".join(headers) + "\n"
            for row in data:
                full_content += " | ".join(row) + "\n"

        workbook.close()

        return UniversalDocument(
            filename=filename,
            file_type=DocumentType.EXCEL,
            content=full_content.strip(),
            sheets=sheets,
            metadata={"sheet_count": len(sheets)},
            file_size=len(file_bytes),
            processing_info={
                "method": "openpyxl",
                "formulas_included": include_formulas,
            },
        )

    def _process_with_pandas(
        self, file_bytes: bytes, filename: str, max_rows: int
    ) -> UniversalDocument:
        file_stream = io.BytesIO(file_bytes)

        # Read all sheets
        excel_data = pd.read_excel(file_stream, sheet_name=None, nrows=max_rows)

        sheets = []
        full_content = ""

        for sheet_name, df in excel_data.items():
            # Convert DataFrame to our format
            headers = df.columns.tolist()
            data = df.fillna("").astype(str).values.tolist()

            sheet = DocumentSheet(
                name=sheet_name,
                data=data,
                headers=[str(h) for h in headers],
                metadata={"rows": len(data), "columns": len(headers)},
            )
            sheets.append(sheet)

            # Add to full content
            full_content += f"\n--- Sheet: {sheet_name} ---\n"
            full_content += " | ".join(str(h) for h in headers) + "\n"
            for row in data:
                full_content += " | ".join(str(cell) for cell in row) + "\n"

        return UniversalDocument(
            filename=filename,
            file_type=DocumentType.EXCEL,
            content=full_content.strip(),
            sheets=sheets,
            metadata={"sheet_count": len(sheets)},
            file_size=len(file_bytes),
            processing_info={"method": "pandas"},
        )

    def extract_text_only(self, file_bytes: bytes, **kwargs) -> str:
        doc = self.process_document(file_bytes, "", **kwargs)
        return doc.content

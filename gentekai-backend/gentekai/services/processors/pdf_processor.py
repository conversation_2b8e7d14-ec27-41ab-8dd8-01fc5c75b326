from typing import List, Optional

from gentekai.models.document import (
    ContentSource,
    DocumentPage,
    DocumentType,
    UniversalDocument,
)
from gentekai.services.pdf_reader_ocr import PDFReaderOCRService
from gentekai.services.processors.base_processor import BaseDocumentProcessor


class PDFProcessor(BaseDocumentProcessor):
    def __init__(self, tesseract_path: Optional[str] = None):
        self.pdf_reader = PDFReaderOCRService(tesseract_path)

    @property
    def supported_types(self) -> List[str]:
        return [".pdf"]

    @property
    def document_type(self) -> DocumentType:
        return DocumentType.PDF

    def can_process(self, file_key: str, content_type: str = None) -> bool:
        return file_key.lower().endswith(".pdf") or (
            content_type and "pdf" in content_type.lower()
        )

    def process_document(
        self, file_bytes: bytes, filename: str, **kwargs
    ) -> UniversalDocument:
        use_ocr = kwargs.get("use_ocr", True)
        ocr_dpi = kwargs.get("ocr_dpi", 300)

        pdf_doc = self.pdf_reader.read_pdf_from_bytes(
            file_bytes, filename, use_ocr, ocr_dpi
        )

        # Convert to universal format
        pages = []
        full_content = ""

        for pdf_page in pdf_doc.pages:
            # Determine primary content source
            if pdf_page.has_text and not pdf_page.is_scanned:
                source = ContentSource.TEXT
                content = pdf_page.text
            elif pdf_page.ocr_text:
                source = ContentSource.OCR
                content = pdf_page.combined_text
            else:
                source = ContentSource.TEXT
                content = pdf_page.text

            page = DocumentPage(
                page_number=pdf_page.page_number,
                content=content,
                source=source,
                metadata={
                    "image_count": pdf_page.image_count,
                    "link_count": pdf_page.link_count,
                    "is_scanned": pdf_page.is_scanned,
                },
            )
            pages.append(page)
            full_content += content + "\n\n"

        return UniversalDocument(
            filename=filename,
            file_type=DocumentType.PDF,
            content=full_content.strip(),
            pages=pages,
            metadata=pdf_doc.metadata,
            file_size=pdf_doc.file_size,
            has_text_content=pdf_doc.has_text_content,
            has_scanned_content=pdf_doc.has_scanned_content,
            processing_info={"ocr_used": use_ocr},
        )

    def extract_text_only(self, file_bytes: bytes, **kwargs) -> str:
        use_ocr = kwargs.get("use_ocr", True)
        return self.pdf_reader.extract_text_only(file_bytes, use_ocr)

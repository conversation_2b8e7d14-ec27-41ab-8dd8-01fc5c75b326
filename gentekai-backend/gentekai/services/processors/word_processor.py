import io
from typing import List

from docx import Document

from gentekai.models.document import (
    ContentSource,
    DocumentPage,
    DocumentType,
    UniversalDocument,
)
from gentekai.services.processors.base_processor import BaseDocumentProcessor


class WordProcessor(BaseDocumentProcessor):
    @property
    def supported_types(self) -> List[str]:
        return [".docx", ".doc"]

    @property
    def document_type(self) -> DocumentType:
        return DocumentType.WORD

    def can_process(self, file_key: str, content_type: str = None) -> bool:
        return any(file_key.lower().endswith(ext) for ext in self.supported_types) or (
            content_type and "word" in content_type.lower()
        )

    def process_document(
        self, file_bytes: bytes, filename: str, **kwargs
    ) -> UniversalDocument:
        extract_tables = kwargs.get("extract_tables", True)
        # extract_images = kwargs.get("extract_images", False)

        try:
            file_stream = io.BytesIO(file_bytes)
            doc = Document(file_stream)

            full_content = ""
            pages = []  # Word doesn't have explicit pages, so we'll use paragraphs/sections
            tables_data = []

            page_num = 1

            # Extract paragraphs
            for para in doc.paragraphs:
                if para.text.strip():
                    page = DocumentPage(
                        page_number=page_num,
                        content=para.text,
                        source=ContentSource.TEXT,
                        metadata={
                            "style": para.style.name if para.style else None,
                            "alignment": str(para.alignment)
                            if para.alignment
                            else None,
                        },
                    )
                    pages.append(page)
                    full_content += para.text + "\n"
                    page_num += 1

            # Extract tables if requested
            if extract_tables:
                for table_idx, table in enumerate(doc.tables):
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        table_data.append(row_data)

                    tables_data.append({"table_index": table_idx, "data": table_data})

                    # Add table content to full text
                    full_content += f"\n--- Table {table_idx + 1} ---\n"
                    for row in table_data:
                        full_content += " | ".join(row) + "\n"

            # Extract document properties
            core_props = doc.core_properties
            metadata = {
                "author": core_props.author,
                "title": core_props.title,
                "subject": core_props.subject,
                "created": core_props.created.isoformat()
                if core_props.created
                else None,
                "modified": core_props.modified.isoformat()
                if core_props.modified
                else None,
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables),
            }

            return UniversalDocument(
                filename=filename,
                file_type=DocumentType.WORD,
                content=full_content.strip(),
                pages=pages,
                metadata=metadata,
                file_size=len(file_bytes),
                processing_info={
                    "tables_extracted": extract_tables,
                    "table_count": len(tables_data),
                    "tables_data": tables_data if extract_tables else None,
                },
            )

        except Exception:
            # Fallback for .doc files or corrupted .docx
            return self._fallback_text_extraction(file_bytes, filename)

    def _fallback_text_extraction(
        self, file_bytes: bytes, filename: str
    ) -> UniversalDocument:
        """Fallback method for files that can't be processed with python-docx"""
        try:
            # Try to extract as much text as possible
            content = "Could not fully process Word document. Limited text extraction available."

            return UniversalDocument(
                filename=filename,
                file_type=DocumentType.WORD,
                content=content,
                metadata={"processing_status": "fallback_mode"},
                file_size=len(file_bytes),
                processing_info={"method": "fallback"},
            )
        except Exception as e:
            raise ValueError(f"Cannot process Word document: {e}")

    def extract_text_only(self, file_bytes: bytes, **kwargs) -> str:
        try:
            file_stream = io.BytesIO(file_bytes)
            doc = Document(file_stream)

            full_text = ""
            for para in doc.paragraphs:
                full_text += para.text + "\n"

            # Add table content
            for table in doc.tables:
                for row in table.rows:
                    row_text = " | ".join(cell.text.strip() for cell in row.cells)
                    full_text += row_text + "\n"

            return full_text.strip()
        except Exception as e:
            return f"Error extracting text from Word document: {e}"

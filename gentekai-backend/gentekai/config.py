from typing import ClassVar, List, Literal

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    ENV: Literal["dev", "prod"] = "dev"
    DATABASE_URL: str

    OPENROUTER_API_KEY: str = ""
    OPENAI_API_KEY: str = ""

    CLERK_ISSUER: str = ""
    CLERK_WEBHOOK_SECRET: str = ""
    CLERK_PUBLISHABLE_KEY: str = ""
    JWKS_PUBLIC_KEY: str = ""

    # SQL_TOOL_URL: str = "http://gtk_db_mcp_server:3002/sse"
    SQL_TOOL_URL: str = "http://localhost:9877/sse"
    GENERAL_LLM_MODEL: str = "anthropic/claude-3.5-haiku"
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3002",
        "http://localhost:3003",
    ]
    UPLOAD_ENDPOINT_URL: str = ""
    UPLOAD_ACCESS_KEY_ID: str = ""
    UPLOAD_ACCESS_KEY: str = ""
    UPLOAD_BUCKET_NAME: str = ""
    UPLOAD_PUBLIC_URL: str = ""

    # Upload Configuration
    MAX_FILE_SIZE: int = 10485760  # 10MB
    PRESIGNED_URL_EXPIRY: int = 3600
    ALLOWED_CONTENT_TYPES: ClassVar[set[str]] = {
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf",
        "text/plain",
        "application/json",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    }
    ALLOWED_EXTENSIONS: ClassVar[set[str]] = {
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".webp",
        ".pdf",
        ".txt",
        ".json",
        ".doc",
        ".docx",
    }
    SLACK_BOT_TOKEN: str = ""
    SLACK_SIGNING_SECRET: str = "",
    SLACK_BOT_ID: str = "U092BRY343B"


settings = Settings(_env_file=".env")

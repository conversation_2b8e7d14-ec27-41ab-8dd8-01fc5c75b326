import uuid
from datetime import datetime
from typing import Optional


def generate_file_key(filename: str, file_extension: Optional[str] = None) -> str:
    """Generate a unique file key for R2 storage"""
    # Create a unique identifier
    unique_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y%m%d")

    # Use provided extension or extract from filename
    if not file_extension and "." in filename:
        file_extension = "." + filename.split(".")[-1].lower()

    # Construct file key with folder structure
    if file_extension:
        file_key = f"uploads/{timestamp}/{unique_id}{file_extension}"
    else:
        file_key = f"uploads/{timestamp}/{unique_id}"

    return file_key

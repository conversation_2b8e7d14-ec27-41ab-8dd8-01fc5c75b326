import logging
from typing import Union

from fastapi import Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.slack import SlackEventCallbackRequest, SlackRequest, SlackUrlVerificationRequest
from gentekai.auth.clerk_jwt import verify_clerk_jwt
from gentekai.db.models import User
from gentekai.db.session import AsyncSessionLocal
from gentekai.services.slack.slack_client import SlackClientHelpers

logger = logging.getLogger(__name__)


async def get_db():
    async with AsyncSessionLocal() as session:
        yield session


async def get_current_user(
    jwt_payload: dict = Depends(verify_clerk_jwt),
    db: AsyncSession = Depends(get_db),
) -> User:
    clerk_user_id = jwt_payload["sub"]
    result = await db.execute(select(User).where(User.clerk_user_id == clerk_user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

async def get_current_slack_user(
    slackRequest: Union[SlackUrlVerificationRequest, SlackEventCallbackRequest],
    db: AsyncSession = Depends(get_db)
) -> User:
    try:
        if slackRequest is SlackUrlVerificationRequest:
            return None
        if slackRequest.event and slackRequest.event.user is None:
            return None
        result = await db.execute(select(User).where(User.slack_user_id == slackRequest.event.user))
        user = result.scalars().first()
        if not user:
            # create new user if not found
            # fetch user details from Slack API 
            slack_user = await SlackClientHelpers.get_slack_user_profile(slackRequest.event.user)
            if not slack_user:
                raise HTTPException(status_code=404, detail="Slack user not found")
            # check if user already exists by email
            existing_user = await db.execute(
                select(User).where(User.email == slack_user.get('email', ''))
            )

            existing_user = existing_user.scalars().first()
            if existing_user:
                # Update existing user with Slack details
                existing_user.slack_user_id = slackRequest.event.user
                existing_user.first_name = slack_user.get('first_name', '')
                existing_user.last_name = slack_user.get('last_name', '')
                await db.commit()
                await db.refresh(existing_user)
                logger.info(f"Syncing Updated existing Slack user: {existing_user.slack_user_id}")
                return existing_user

            # Create a new User instance
            logger.info(f"Creating new user for Slack user: {slack_user}")
            user = User(slack_user_id=slackRequest.event.user, 
                        email=slack_user.get('email', ''),
                        first_name=slack_user.get('first_name', ''),
                        last_name=slack_user.get('last_name', ''))
            db.add(user)
            await db.commit()
            await db.refresh(user)
            logger.info(f"Created new Slack user: {user.slack_user_id}")   
        return user
    except Exception as e:
        logger.error(f"Error fetching Slack user: {e}", exc_info=True)
        return None
    


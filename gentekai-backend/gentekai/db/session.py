from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from gentekai.config import settings

async_engine = create_async_engine(settings.DATABASE_URL, echo=False, future=True)

AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


# For use in scripts or on app startup/shutdown
async def init_db():
    from gentekai.db.models import Base

    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("Database initialized.")


async def close_db():
    await async_engine.dispose()

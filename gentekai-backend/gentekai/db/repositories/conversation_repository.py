import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, distinct, func, select, update
from sqlalchemy.orm import selectinload

from gentekai.db.models import Conversation, Message
from gentekai.db.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class ConversationRepository(BaseRepository):
    def __init__(self, db):
        super().__init__(Conversation, db)

    async def get_by_user_id(
        self,
        user_id: str,
        limit: int = 20,
        offset: int = 0,
        search: Optional[str] = None,
        order_by: str = "updated_at",
        order_dir: str = "desc",
    ) -> Tuple[List[Conversation], int]:
        """Get conversations for a user with pagination and search"""

        # Base query
        query = select(Conversation).where(Conversation.user_id == user_id)

        # Add search filter
        if search:
            query = query.where(Conversation.title.ilike(f"%{search}%"))

        # Add ordering
        order_column = getattr(Conversation, order_by, Conversation.updated_at)
        if order_dir == "desc":
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(order_column)

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # Get paginated results
        query = query.offset(offset).limit(limit)
        result = await self.db.execute(query)
        conversations = result.scalars().all()

        return conversations, total

    async def get_with_messages(
        self, conversation_id: str, user_id: str
    ) -> Optional[Conversation]:
        """Get conversation with messages loaded"""

        stmt = (
            select(Conversation)
            .options(selectinload(Conversation.messages))
            .where(
                and_(
                    Conversation.id == conversation_id, Conversation.user_id == user_id
                )
            )
        )

        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_id(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation by ID"""

        stmt = select(Conversation).where(Conversation.id == conversation_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_slack_thread_id(
        self, slack_thread_id: str, user_id: str
    ) -> Optional[Conversation]:
        """Get conversation by Slack thread ID"""
        logger.info(
            f"Fetching conversation by Slack thread ID: {slack_thread_id} for user {user_id}"
        )

        stmt = select(Conversation).where(
            Conversation.slack_thread_id == slack_thread_id,
            Conversation.user_id == user_id,
        )
        result = await self.db.execute(stmt)
        if result is None:
            logger.warning(
                f"No conversation found for Slack thread ID: {slack_thread_id} and user {user_id}"
            )

        return result.scalar_one_or_none()

    async def update_last_message_time(self, conversation_id: str):
        """Update the updated_at time for a conversation"""

        stmt = (
            update(Conversation)
            .where(Conversation.id == conversation_id)
            .values(updated_at=datetime.utcnow())
        )

        await self.db.execute(stmt)
        await self.db.commit()

    async def get_conversation_stats(self, conversation_id: str) -> Dict[str, Any]:
        """Get statistics for a conversation"""

        # Get message count and other stats
        stmt = select(
            func.count(Message.id).label("message_count"),
            func.count(Message.id)
            .filter(Message.sender == "user")
            .label("user_messages"),
            func.count(Message.id)
            .filter(Message.sender == "assistant")
            .label("assistant_messages"),
        ).where(Message.conversation_id == conversation_id)

        result = await self.db.execute(stmt)
        stats = result.first()

        return {
            "message_count": stats.message_count or 0,
            "user_messages": stats.user_messages or 0,
            "assistant_messages": stats.assistant_messages or 0,
        }

    async def bulk_update(
        self, conversation_ids: List[str], user_id: str, data: Dict[str, Any]
    ) -> int:
        """Bulk update conversations"""

        # Add updated_at to the data
        data["updated_at"] = datetime.utcnow()

        stmt = (
            update(Conversation)
            .where(
                and_(
                    Conversation.id.in_(conversation_ids),
                    Conversation.user_id == user_id,
                )
            )
            .values(**data)
        )

        result = await self.db.execute(stmt)
        await self.db.commit()
        return result.rowcount

    async def get_conversations_stats_batch(
        self, conversation_ids: List[str]
    ) -> Dict[str, dict]:
        """
        Get conversation statistics for multiple conversations in a single query
        Returns a dict mapping conversation_id -> stats_dict
        """
        try:
            stmt = (
                select(
                    Message.conversation_id,
                    func.count(Message.id).label("message_count"),
                    func.count(distinct(Message.sender)).label("unique_senders"),
                    func.min(Message.created_at).label("first_message_at"),
                    func.max(Message.created_at).label("last_message_at"),
                )
                .where(Message.conversation_id.in_(conversation_ids))
                .group_by(Message.conversation_id)
            )

            result = await self.db.execute(stmt)
            rows = result.all()

            # Convert to dict mapping conversation_id -> stats
            stats_dict = {}
            for row in rows:
                stats_dict[row.conversation_id] = {
                    "message_count": row.message_count,
                    "unique_senders": row.unique_senders,
                    "first_message_at": row.first_message_at,
                    "last_message_at": row.last_message_at,
                }

            # Fill in missing conversation IDs with empty stats
            for conv_id in conversation_ids:
                if conv_id not in stats_dict:
                    stats_dict[conv_id] = {
                        "message_count": 0,
                        "unique_senders": 0,
                        "first_message_at": None,
                        "last_message_at": None,
                    }

            return stats_dict

        except Exception as e:
            logger.error(f"Failed to get conversation stats batch: {e}")
            # Return empty stats for all conversations
            return {
                conv_id: {
                    "message_count": 0,
                    "unique_senders": 0,
                    "first_message_at": None,
                    "last_message_at": None,
                }
                for conv_id in conversation_ids
            }

    async def get_by_user_id_with_stats(
        self,
        user_id: str,
        limit: int = 20,
        offset: int = 0,
        search: Optional[str] = None,
        order_by: str = "updated_at",
        order_dir: str = "desc",
    ) -> Tuple[List, int]:
        """
        Enhanced version that gets conversations with message stats in fewer queries
        """
        try:
            # Base query for conversations
            base_query = select(Conversation).where(Conversation.user_id == user_id)

            # Add search filter
            if search:
                base_query = base_query.where(Conversation.title.ilike(f"%{search}%"))

            # Count total
            count_stmt = select(func.count()).select_from(base_query.subquery())
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()

            # Get conversations with ordering and pagination
            order_column = getattr(Conversation, order_by, Conversation.updated_at)
            if order_dir == "desc":
                order_column = order_column.desc()
            else:
                order_column = order_column.asc()

            conversations_stmt = (
                base_query.order_by(order_column).offset(offset).limit(limit)
            )

            conversations_result = await self.db.execute(conversations_stmt)
            conversations = conversations_result.scalars().all()

            return conversations, total

        except Exception as e:
            logger.error(f"Failed to get conversations by user ID with stats: {e}")
            return [], 0

import logging
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, select

from gentekai.db.models import Message
from gentekai.db.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class MessageRepository(BaseRepository):
    def __init__(self, db):
        super().__init__(Message, db)

    async def get_by_conversation(
        self, conversation_id: str, limit: int = 50, offset: int = 0, order: str = "asc"
    ) -> List[Message]:
        """Get messages for a conversation"""

        query = select(Message).where(Message.conversation_id == conversation_id)

        if order == "desc":
            query = query.order_by(desc(Message.created_at))
        else:
            query = query.order_by(Message.created_at)

        query = query.offset(offset).limit(limit)

        result = await self.db.execute(query)
        messages = result.scalars().all()

        # If we got them in desc order but want chronological, reverse
        if order == "asc" and messages:
            messages = list(reversed(messages))

        return messages

    async def get_recent_by_conversation(
        self, conversation_id: str, limit: int = 20
    ) -> List[Message]:
        """Get recent messages for a conversation (chronological order)"""

        # Get the most recent messages in desc order, then reverse
        stmt = (
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .order_by(desc(Message.created_at))
            .limit(limit)
        )

        result = await self.db.execute(stmt)
        messages = result.scalars().all()

        # Return in chronological order (oldest first)
        return list(reversed(messages))

    async def create_message(
        self,
        conversation_id: str,
        sender: str,
        content: str,
        embedding: Optional[List[float]] = None,
    ) -> Message:
        """Create a new message"""

        message_data = {
            "conversation_id": conversation_id,
            "sender": sender,
            "content": content,
        }

        if embedding:
            message_data["embedding"] = embedding

        return await self.create(message_data)

    async def get_recent_by_conversations_batch(
        self, conversation_ids: List[str]
    ) -> Dict[str, "Message"]:
        """
        Get the most recent message for multiple conversations in a single query
        Returns a dict mapping conversation_id -> Message
        """
        try:
            # Subquery to get the max created_at for each conversation
            subquery = (
                select(
                    Message.conversation_id,
                    func.max(Message.created_at).label("max_created_at"),
                )
                .where(Message.conversation_id.in_(conversation_ids))
                .group_by(Message.conversation_id)
                .subquery()
            )

            # Main query to get the actual messages
            stmt = (
                select(Message)
                .join(
                    subquery,
                    (Message.conversation_id == subquery.c.conversation_id)
                    & (Message.created_at == subquery.c.max_created_at),
                )
                .where(Message.conversation_id.in_(conversation_ids))
            )

            result = await self.db.execute(stmt)
            messages = result.scalars().all()

            # Convert to dict mapping conversation_id -> Message
            return {msg.conversation_id: msg for msg in messages}

        except Exception as e:
            logger.error(f"Failed to get recent messages batch: {e}")
            return {}

    async def get_conversation_analytics(self, conversation_id: str) -> Dict[str, Any]:
        """Get analytics for messages in a conversation"""

        stmt = select(
            func.count(Message.id).label("total_messages"),
            func.count(Message.id)
            .filter(Message.sender == "user")
            .label("user_messages"),
            func.count(Message.id)
            .filter(Message.sender == "assistant")
            .label("assistant_messages"),
            func.min(Message.created_at).label("first_message"),
            func.max(Message.created_at).label("last_message"),
        ).where(Message.conversation_id == conversation_id)

        result = await self.db.execute(stmt)
        stats = result.first()

        # Calculate session duration
        session_duration = 0
        if stats.first_message and stats.last_message:
            duration = stats.last_message - stats.first_message
            session_duration = duration.total_seconds() / 60  # minutes

        return {
            "total_messages": stats.total_messages or 0,
            "user_messages": stats.user_messages or 0,
            "assistant_messages": stats.assistant_messages or 0,
            "session_duration_minutes": session_duration,
            "first_message_at": stats.first_message,
            "last_message_at": stats.last_message,
        }

    async def search_messages(
        self, conversation_id: str, query: str, limit: int = 20
    ) -> List[Message]:
        """Search messages in a conversation"""

        stmt = (
            select(Message)
            .where(
                and_(
                    Message.conversation_id == conversation_id,
                    Message.content.ilike(f"%{query}%"),
                )
            )
            .order_by(desc(Message.created_at))
            .limit(limit)
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

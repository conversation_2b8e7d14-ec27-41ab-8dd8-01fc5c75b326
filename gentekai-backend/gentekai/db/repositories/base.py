import logging
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from uuid import UUID

from sqlalchemy import delete, func, update
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from gentekai.db.models.base import Base

logger = logging.getLogger(__name__)

# Type variable for model
ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    """Base repository class with common CRUD operations"""

    def __init__(self, model: Type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db

    async def create(self, obj_data: Dict[str, Any]) -> ModelType:
        """Create a new record"""
        try:
            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            await self.db.commit()
            await self.db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Failed to create {self.model.__name__}: {e}")
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error creating {self.model.__name__}: {e}")
            raise

    async def get_by_id(self, id: Union[str, UUID]) -> Optional[ModelType]:
        """Get record by ID"""
        try:
            stmt = select(self.model).where(self.model.id == id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get {self.model.__name__} by id {id}: {e}")
            raise

    async def get_all(
        self, limit: int = 100, offset: int = 0, order_by: Optional[str] = None
    ) -> List[ModelType]:
        """Get all records with pagination"""
        try:
            stmt = select(self.model).limit(limit).offset(offset)

            if order_by and hasattr(self.model, order_by):
                order_column = getattr(self.model, order_by)
                stmt = stmt.order_by(order_column.desc())

            result = await self.db.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get all {self.model.__name__}: {e}")
            raise

    async def update(
        self, id: Union[str, UUID], update_data: Dict[str, Any]
    ) -> Optional[ModelType]:
        """Update record by ID"""
        try:
            # Get existing record
            obj = await self.get_by_id(id)
            if not obj:
                return None

            # Update fields
            for key, value in update_data.items():
                if hasattr(obj, key):
                    setattr(obj, key, value)

            await self.db.commit()
            await self.db.refresh(obj)
            return obj
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update {self.model.__name__} {id}: {e}")
            raise

    async def delete(self, id: Union[str, UUID]) -> bool:
        """Delete record by ID"""
        try:
            obj = await self.get_by_id(id)
            if not obj:
                return False

            await self.db.delete(obj)
            await self.db.commit()
            return True
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete {self.model.__name__} {id}: {e}")
            raise

    async def count(self) -> int:
        """Get total count of records"""
        try:
            stmt = select(func.count(self.model.id))
            result = await self.db.execute(stmt)
            return result.scalar()
        except Exception as e:
            logger.error(f"Failed to count {self.model.__name__}: {e}")
            raise

    async def exists(self, id: Union[str, UUID]) -> bool:
        """Check if record exists by ID"""
        try:
            stmt = select(self.model.id).where(self.model.id == id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none() is not None
        except Exception as e:
            logger.error(
                f"Failed to check existence of {self.model.__name__} {id}: {e}"
            )
            raise

    async def bulk_create(self, objects_data: List[Dict[str, Any]]) -> List[ModelType]:
        """Create multiple records"""
        try:
            db_objects = [self.model(**obj_data) for obj_data in objects_data]
            self.db.add_all(db_objects)
            await self.db.commit()

            # Refresh all objects
            for obj in db_objects:
                await self.db.refresh(obj)

            return db_objects
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Failed to bulk create {self.model.__name__}: {e}")
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error in bulk create {self.model.__name__}: {e}")
            raise

    async def bulk_update(
        self, updates: List[Dict[str, Any]], id_field: str = "id"
    ) -> int:
        """Bulk update records"""
        try:
            if not updates:
                return 0

            # Use SQLAlchemy bulk update
            stmt = update(self.model)
            result = await self.db.execute(stmt, updates)
            await self.db.commit()

            return result.rowcount
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to bulk update {self.model.__name__}: {e}")
            raise

    async def bulk_delete(self, ids: List[Union[str, UUID]]) -> int:
        """Bulk delete records by IDs"""
        try:
            if not ids:
                return 0

            stmt = delete(self.model).where(self.model.id.in_(ids))
            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to bulk delete {self.model.__name__}: {e}")
            raise

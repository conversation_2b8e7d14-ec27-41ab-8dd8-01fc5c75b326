# ============================================================================
# 📄 app/db/repositories/user_repository.py - User Repository
# ============================================================================
import logging
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from gentekai.db.models import User
from gentekai.db.models.users import UserRole
from gentekai.db.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository[User]):
    def __init__(self, db: AsyncSession):
        super().__init__(User, db)

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create a new user"""
        try:
            user = User(**user_data)
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            return user
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Failed to create user due to integrity error: {e}")
            raise ValueError("User with this email or clerk_user_id already exists")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create user: {e}")
            raise

    async def get_by_clerk_id(self, clerk_user_id: str) -> Optional[User]:
        """Get user by Clerk user ID"""
        try:
            stmt = select(User).where(User.clerk_user_id == clerk_user_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get user by clerk_id {clerk_user_id}: {e}")
            raise

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        try:
            stmt = select(User).where(User.email == email.lower())
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            raise

    async def update_user(
        self, user_id: str, update_data: Dict[str, Any]
    ) -> Optional[User]:
        """Update user by ID"""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return None

            for key, value in update_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)

            await self.db.commit()
            await self.db.refresh(user)
            return user
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update user {user_id}: {e}")
            raise

    async def update_by_clerk_id(
        self, clerk_user_id: str, update_data: Dict[str, Any]
    ) -> Optional[User]:
        """Update user by Clerk ID"""
        try:
            user = await self.get_by_clerk_id(clerk_user_id)
            if not user:
                return None

            for key, value in update_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)

            await self.db.commit()
            await self.db.refresh(user)
            return user
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update user by clerk_id {clerk_user_id}: {e}")
            raise

    async def delete_by_clerk_id(self, clerk_user_id: str) -> bool:
        """Delete user by Clerk ID"""
        try:
            user = await self.get_by_clerk_id(clerk_user_id)
            if not user:
                return False

            await self.db.delete(user)
            await self.db.commit()
            return True
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete user by clerk_id {clerk_user_id}: {e}")
            raise

    async def search_users(
        self, query: str, limit: int = 20, offset: int = 0
    ) -> Tuple[List[User], int]:
        """Search users by name or email"""
        try:
            # Search query
            search_filter = or_(
                User.first_name.ilike(f"%{query}%"),
                User.last_name.ilike(f"%{query}%"),
                User.email.ilike(f"%{query}%"),
            )

            # Get total count
            count_stmt = select(func.count(User.id)).where(search_filter)
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()

            # Get users
            stmt = (
                select(User)
                .where(search_filter)
                .order_by(User.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
            result = await self.db.execute(stmt)
            users = result.scalars().all()

            return list(users), total
        except Exception as e:
            logger.error(f"Failed to search users with query '{query}': {e}")
            raise

    async def get_users_by_role(
        self, role: UserRole, limit: int = 50, offset: int = 0
    ) -> Tuple[List[User], int]:
        """Get users by role"""
        try:
            # Get total count
            count_stmt = select(func.count(User.id)).where(User.role == role)
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()

            # Get users
            stmt = (
                select(User)
                .where(User.role == role)
                .order_by(User.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
            result = await self.db.execute(stmt)
            users = result.scalars().all()

            return list(users), total
        except Exception as e:
            logger.error(f"Failed to get users by role {role}: {e}")
            raise

    async def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics"""
        try:
            # Total users
            total_stmt = select(func.count(User.id))
            total_result = await self.db.execute(total_stmt)
            total_users = total_result.scalar()

            # Users by role
            role_stmt = select(User.role, func.count(User.id)).group_by(User.role)
            role_result = await self.db.execute(role_stmt)
            role_counts = {role.value: count for role, count in role_result.fetchall()}

            # Recent users (last 30 days)
            from datetime import datetime, timedelta

            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            recent_stmt = select(func.count(User.id)).where(
                User.created_at >= thirty_days_ago
            )
            recent_result = await self.db.execute(recent_stmt)
            recent_users = recent_result.scalar()

            return {
                "total_users": total_users,
                "users_by_role": role_counts,
                "recent_users_30_days": recent_users,
                "admin_count": role_counts.get("admin", 0),
                "user_count": role_counts.get("user", 0),
            }
        except Exception as e:
            logger.error(f"Failed to get user stats: {e}")
            raise

    async def check_email_exists(
        self, email: str, exclude_clerk_id: Optional[str] = None
    ) -> bool:
        """Check if email already exists (optionally excluding a specific user)"""
        try:
            stmt = select(User.id).where(User.email == email.lower())

            if exclude_clerk_id:
                stmt = stmt.where(User.clerk_user_id != exclude_clerk_id)

            result = await self.db.execute(stmt)
            return result.scalar_one_or_none() is not None
        except Exception as e:
            logger.error(f"Failed to check email existence {email}: {e}")
            raise

    async def get_or_create_by_clerk_id(
        self, clerk_user_id: str, user_data: Dict[str, Any]
    ) -> Tuple[User, bool]:
        """Get existing user or create new one by Clerk ID"""
        try:
            # Try to get existing user
            existing_user = await self.get_by_clerk_id(clerk_user_id)
            if existing_user:
                return existing_user, False

            # Create new user
            user_data["clerk_user_id"] = clerk_user_id
            new_user = await self.create_user(user_data)
            return new_user, True

        except Exception as e:
            logger.error(
                f"Failed to get_or_create user by clerk_id {clerk_user_id}: {e}"
            )
            raise

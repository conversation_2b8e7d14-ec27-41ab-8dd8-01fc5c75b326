from datetime import datetime

from sqlalchemy import UUID, Boolean, Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.dialects.postgresql import JSONB

from gentekai.db.models.base import Base


class AgentToolLog(Base):
    __tablename__ = "agent_tool_logs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(UUID, ForeignKey("users.id"), nullable=False)
    tool_name = Column(String(100))
    params = Column(JSONB)
    result = Column(JSONB)
    success = Column(Boolean)
    timestamp = Column(DateTime, default=datetime.utcnow)

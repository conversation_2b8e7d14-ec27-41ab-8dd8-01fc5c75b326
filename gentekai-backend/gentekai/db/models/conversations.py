import uuid
from datetime import datetime

from sqlalchemy import (
    UUI<PERSON>,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
)
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class Conversation(Base):
    __tablename__ = "conversations"

    # Primary fields
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False, default="New Conversation")
    slack_thread_id = Column(
        String(64),
        nullable=True,
    )

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Soft delete fields
    is_deleted = Column(<PERSON>olean, default=False, nullable=False)
    deleted_at = Column(DateTime, nullable=True)

    # Archive fields
    is_archived = Column(Boolean, default=False, nullable=False)
    archived_at = Column(DateTime, nullable=True)

    # Additional metadata
    message_count = Column(
        Integer, default=0, nullable=False
    )  # Denormalized for performance
    last_message_at = Column(DateTime, nullable=True)  # Cache for quick access

    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship(
        "Message",
        back_populates="conversation",
        cascade="all, delete-orphan",  # Delete messages when conversation is deleted
        order_by="Message.created_at",
    )

    # Indexes for performance
    __table_args__ = (
        # Composite indexes for common queries
        Index("idx_conversations_user_active", "user_id", "is_deleted", "is_archived"),
        Index("idx_conversations_user_updated", "user_id", "updated_at"),
        Index("idx_conversations_user_created", "user_id", "created_at"),
        # Individual indexes
        Index("idx_conversations_deleted", "is_deleted"),
        Index("idx_conversations_archived", "is_archived"),
        Index("idx_conversations_last_message", "last_message_at"),
        # For search functionality
        Index(
            "idx_conversations_title_search", "title"
        ),  # Consider gin/gist for full-text search
    )

    def __repr__(self):
        return f"<Conversation(id={self.id}, title='{self.title[:30]}...', user_id={self.user_id})>"

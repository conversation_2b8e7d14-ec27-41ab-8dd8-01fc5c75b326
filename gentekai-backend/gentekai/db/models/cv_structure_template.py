from app.db.models import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>olean, Column, DateTime, Integer, String
from sqlalchemy.sql import func


class CvStructureTemplate(Base):
    __tablename__ = "cv_structure_templates"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    description = Column(String, nullable=True)
    template = Column(JSON, nullable=False)
    created_at = Column(DateTime, nullable=True, default=func.now())
    updated_at = Column(
        DateTime, nullable=True, default=func.now(), onupdate=func.now()
    )
    is_active = Column(Boolean, nullable=True, default=True)
    is_default = Column(Boolean, nullable=True, default=False)

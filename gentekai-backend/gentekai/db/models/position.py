import enum
from datetime import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import (
    DECIMAL,
    UUID,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from gentekai.db.models.base import Base


class PositionStatus(enum.Enum):
    DRAFT = "draft"
    OPEN = "open"
    ON_HOLD = "on_hold"
    FILLED = "filled"
    CANCELLED = "cancelled"


class EmploymentType(enum.Enum):
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    INTERN = "intern"
    FREELANCE = "freelance"


class ExperienceLevel(enum.Enum):
    ENTRY = "entry"
    MID = "mid"
    SENIOR = "senior"
    LEAD = "lead"
    EXECUTIVE = "executive"


class Currency(enum.Enum):
    USD = "USD"
    EUR = "EUR"
    VND = "VND"


class Position(Base):
    __tablename__ = "positions"

    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=True)
    hiring_manager_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )  # USER role

    # Job details
    job_description = Column(Text, nullable=False)
    requirements = Column(Text)
    nice_to_have = Column(Text)
    responsibilities = Column(Text)

    # Position specifics
    employment_type = Column(Enum(EmploymentType), default=EmploymentType.FULL_TIME)
    experience_level = Column(Enum(ExperienceLevel), default=ExperienceLevel.MID)
    salary_min = Column(DECIMAL(10, 2))
    salary_max = Column(DECIMAL(10, 2))
    currency = Column(Enum(Currency), default=Currency.EUR)
    location = Column(String(100))
    remote_allowed = Column(Boolean, default=False)

    # Status and dates
    status = Column(Enum(PositionStatus), default=PositionStatus.DRAFT)
    posted_date = Column(DateTime)
    closing_date = Column(DateTime)
    positions_available = Column(Integer, default=1)

    # Vector embeddings for semantic search
    embedding = Column(
        Vector(1536), nullable=True
    )  # pgvector extension; 1536-dim OpenAI embeddings

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )  # USER role

    # Relationships
    department = relationship("Department", back_populates="positions")
    hiring_manager = relationship("User", foreign_keys=[hiring_manager_id])
    created_by = relationship("User", foreign_keys=[created_by_id])
    applications = relationship("Application", back_populates="position")

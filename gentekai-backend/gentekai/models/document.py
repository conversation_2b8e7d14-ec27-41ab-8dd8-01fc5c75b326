from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List


class DocumentType(Enum):
    PDF = "pdf"
    EXCEL = "excel"
    WORD = "word"
    IMAGE = "image"
    POWERPOINT = "powerpoint"
    TEXT = "text"
    CSV = "csv"
    UNKNOWN = "unknown"


class ContentSource(Enum):
    TEXT = "text"
    OCR = "ocr"
    FORMULA = "formula"
    METADATA = "metadata"


@dataclass
class DocumentPage:
    page_number: int
    content: str
    source: ContentSource
    metadata: Dict[str, Any] = None
    images: List[str] = None  # Base64 encoded images
    tables: List[Dict[str, Any]] = None


@dataclass
class DocumentSheet:
    """For Excel/CSV - represents a sheet/tab"""

    name: str
    data: List[List[str]]  # 2D array of cell values
    headers: List[str] = None
    metadata: Dict[str, Any] = None


@dataclass
class UniversalDocument:
    filename: str
    file_type: DocumentType
    content: str  # Combined text content
    pages: List[DocumentPage] = None
    sheets: List[DocumentSheet] = None  # For Excel/CSV
    metadata: Dict[str, Any] = None
    file_size: int = 0
    has_text_content: bool = True
    has_scanned_content: bool = False
    processing_info: Dict[str, Any] = None

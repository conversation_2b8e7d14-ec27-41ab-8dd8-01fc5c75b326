from datetime import datetime

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.endpoints import chat, conversations, upload
from gentekai.deps import get_current_user, get_db
from gentekai.services.chat_service import ChatService
from gentekai.services.conversation_service import ConversationService

api_router = APIRouter()


@api_router.get("/health")
async def health_check(db: AsyncSession = Depends(get_db)):
    chat_service = ChatService(db)
    conversation_service = ConversationService(db)

    chat_health = await chat_service.health_check()
    conversation_health = await conversation_service.health_check()

    return {
        "chat_service": chat_health,
        "conversation_service": conversation_health,
        "overall_status": "healthy"
        if chat_health["status"] == "healthy"
        else "degraded",
        "timestamp": datetime.utcnow().isoformat(),
    }


# Protected endpoints (auth required)
api_router.include_router(
    chat.router, prefix="/chat", tags=["chat"], dependencies=[Depends(get_current_user)]
)

api_router.include_router(
    upload.router,
    prefix="/upload",
    tags=["upload"],
    dependencies=[Depends(get_current_user)],
)

api_router.include_router(
    conversations.router,
    prefix="/conversations",
    tags=["conversations"],
    dependencies=[Depends(get_current_user)],
)

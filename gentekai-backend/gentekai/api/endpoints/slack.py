import hashlib
import hmac
import json
import logging
import time

from botocore.exceptions import ValidationError
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.slack import (
    SlackEventCallbackRequest,
    SlackRequest,
    SlackUrlVerificationRequest,
)
from gentekai.config import settings
from gentekai.db.models.users import User
from gentekai.deps import get_current_slack_user, get_db
from gentekai.services.slack_service import process_slack_event

logger = logging.getLogger(__name__)
slack_router = APIRouter()


def verify_slack_request(body: bytes, timestamp: str, slack_signature: str) -> bool:
    if not timestamp or not slack_signature:
        return False

    slack_signing_secret = settings.SLACK_SIGNING_SECRET
    if not slack_signing_secret:
        raise ValueError("SLACK_SIGNING_SECRET not found")

    # Prevent replay attacks
    current_time = int(time.time())
    if abs(current_time - int(timestamp)) > 300:  # 5 minutes
        return False

    sig_basestring = f"v0:{timestamp}:{body.decode('utf-8')}"
    computed_signature = (
        "v0="
        + hmac.new(
            slack_signing_secret.encode(), sig_basestring.encode(), hashlib.sha256
        ).hexdigest()
    )

    return hmac.compare_digest(computed_signature, slack_signature)


async def verify_and_parse_slack_request(request: Request) -> SlackRequest:
    """
    FastAPI dependency that verifies Slack signature and parses the request
    """
    # Get raw request data
    body = await request.body()
    logger.info(f"request body: {body}\n\n")
    timestamp = request.headers.get("X-Slack-Request-Timestamp")
    slack_signature = request.headers.get("X-Slack-Signature")

    # Verify the request
    if not verify_slack_request(body, timestamp, slack_signature):
        raise HTTPException(status_code=401, detail="Invalid Slack signature")

    # Parse and validate JSON
    try:
        data = json.loads(body)

        if data.get("type") == "url_verification":
            return SlackUrlVerificationRequest(**data)
        elif data.get("type") == "event_callback":
            # remove bot mention from event text
            if "event" in data and "text" in data["event"]:
                event_text = data["event"]["text"]
                bot_id = settings.SLACK_BOT_ID
                if bot_id in event_text:
                    data["event"]["text"] = event_text.replace(f"<@{bot_id}>", "").strip()
            # remove bot mention from event message
            if "event" in data and "message" in data["event"]:
                message_text = data["event"]["message"].get("text", "")
                bot_id = settings.SLACK_BOT_ID
                if bot_id in message_text:
                    data["event"]["message"]["text"] = message_text.replace(
                        f"<@{bot_id}>", ""
                    ).strip() 
            return SlackEventCallbackRequest(**data)
        else:
            raise HTTPException(status_code=400, detail="Unknown request type")

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON")
    except ValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))


@slack_router.post("/events", status_code=status.HTTP_202_ACCEPTED)
async def slack_events(
    slack_request: SlackRequest = Depends(verify_and_parse_slack_request),
    user: User = Depends(get_current_slack_user),
    db: AsyncSession = Depends(get_db),
):

    try:
        if isinstance(slack_request, SlackUrlVerificationRequest):
            return {"challenge": slack_request.challenge}
        elif isinstance(slack_request, SlackEventCallbackRequest):
            await process_slack_event(slack_request, user=user, db=db)
            return {"status": "ok"}
        else:
            raise HTTPException(status_code=400, detail="Unknown request type")
    except ValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON")

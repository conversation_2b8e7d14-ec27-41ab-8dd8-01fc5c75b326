import logging
from typing import List

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.chat import ChatStreamRequest
from gentekai.db.models import User
from gentekai.deps import get_current_user, get_db
from gentekai.services.chat_service import ChatService
from gentekai.services.conversation_service import ConversationService
from gentekai.services.document_context_service import DocumentContextService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/stream", response_class=StreamingResponse)
async def stream_chat(
    request: ChatStreamRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Stream chat with document context"""
    logger.info("Received chat request: %s", request)

    # Validate conversation
    conversation_service = ConversationService(db)
    conversation = await conversation_service.get_conversation_by_id(
        conversation_id=request.conversation_id, user_id=str(current_user.id)
    )

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Prepare document context if files provided
    document_context = ""

    if request.file_keys and request.include_file_analysis:
        try:
            doc_context_service = DocumentContextService()
            context_data = await doc_context_service.prepare_document_context(
                file_keys=request.file_keys,
                user_prompt=request.prompt,
                max_context_length=request.max_context_length,
            )
            document_context = context_data["context"]
            context_info = {
                "files_processed": context_data["files_processed"],
                "strategy_used": context_data["strategy_used"],
                "context_length": context_data["total_context_length"],
            }

            logger.info(f"Document context prepared: {context_info}")

        except Exception as e:
            logger.error(f"Failed to prepare document context: {e}")
            # Continue without document context
            pass

    # Enhanced prompt with document context
    enhanced_prompt = request.prompt
    if document_context:
        enhanced_prompt = f"User uploaded documents with these context: {document_context}\n\n\n\nUser Question: {request.prompt}\n\nPlease answer based on the provided document context above."

    # Use ChatService for streaming
    chat_service = ChatService(db)

    async def generate():
        async for chunk in chat_service.stream_chat_with_agents(
            conv_id=request.conversation_id,
            user=current_user,
            prompt=request.prompt,
            enhanced_prompt=enhanced_prompt,
            file_keys=request.file_keys,
            document_context=document_context,
        ):
            if chunk != "\n\ndata: [DONE]\n\n":
                yield f"{chunk}"

    return StreamingResponse(generate(), media_type="text/plain")


@router.post("/context/preview")
async def preview_document_context(
    file_keys: List[str], prompt: str, current_user: User = Depends(get_current_user)
):
    """Preview what context would be generated from documents"""
    try:
        doc_context_service = DocumentContextService()
        context_data = await doc_context_service.prepare_document_context(
            file_keys=file_keys, user_prompt=prompt, max_context_length=16000
        )
        return context_data
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to preview context: {str(e)}"
        )

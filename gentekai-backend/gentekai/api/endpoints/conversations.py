import logging
from typing import List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

# Updated Schemas
from gentekai.api.schemas.conversation import (
    BulkOperationRequest,
    BulkOperationResult,
    ConversationAnalyticsSchema,
    ConversationCreateRequest,
    ConversationListItemSchema,
    ConversationListResponse,
    ConversationNoMessageSchema,
    ConversationSchema,
    ConversationUpdateRequest,
    MessageSchema,
)
from gentekai.db.models import User
from gentekai.deps import get_current_user, get_db
from gentekai.services.conversation_service import ConversationService
from gentekai.storage.r2 import R2StorageService

logger = logging.getLogger(__name__)
router = APIRouter()


# ============================================================================
# CONVERSATION CRUD ENDPOINTS
# ============================================================================


@router.post(
    "/", response_model=ConversationNoMessageSchema, status_code=status.HTTP_201_CREATED
)
async def create_conversation(
    request: ConversationCreateRequest = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Create a new conversation"""
    try:
        conversation_service = ConversationService(db)
        conversation = await conversation_service.create_conversation(
            user_id=str(current_user.id),
            title=request.title or "New Conversation",
            initial_message=request.initial_message,
        )

        return ConversationNoMessageSchema(
            id=conversation.id,
            title=conversation.title,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at,
            message_count=1 if request.initial_message else 0,
        )

    except Exception as e:
        logger.error(f"Failed to create conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create conversation",
        )


@router.get("/", response_model=ConversationListResponse)
async def list_conversations(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    limit: int = Query(
        20, ge=1, le=100, description="Number of conversations to return"
    ),
    offset: int = Query(0, ge=0, description="Number of conversations to skip"),
    search: Optional[str] = Query(
        None, max_length=100, description="Search in conversation titles"
    ),
    order_by: str = Query("updated_at", regex="^(created_at|updated_at|title)$"),
    order_dir: str = Query("desc", regex="^(asc|desc)$"),
):
    """List user's conversations with pagination and search"""
    try:
        conversation_service = ConversationService(db)
        conversations, total = await conversation_service.list_user_conversations(
            user_id=str(current_user.id),
            limit=limit,
            offset=offset,
            search=search,
            order_by=order_by,
            order_dir=order_dir,
        )

        # Convert to response schema
        conversation_items = [
            ConversationListItemSchema(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                message_count=getattr(conv, "message_count", 0),
                last_message_at=getattr(conv, "last_message_at", None),
                last_message_preview=getattr(conv, "last_message_preview", None),
                is_archived=getattr(conv, "is_archived", False),
            )
            for conv in conversations
        ]

        return ConversationListResponse(
            conversations=conversation_items,
            total=total,
            limit=limit,
            offset=offset,
            has_next=(offset + limit) < total,
        )

    except Exception as e:
        logger.error(f"Failed to list conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations",
        )


@router.get("/{conversation_id}", response_model=ConversationSchema)
async def get_conversation(
    conversation_id: str = Path(..., description="Conversation ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Get a specific conversation with messages"""
    try:
        conversation_service = ConversationService(db)
        conversation = await conversation_service.get_conversation_by_id(
            conversation_id=conversation_id, user_id=str(current_user.id)
        )

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
            )
        # Get storage service and generate presigned URL
        storage_service = R2StorageService()

        # Convert messages to schema with file URLs
        messages = []
        for msg in conversation.messages or []:
            # Start with the original extra data
            enhanced_extra = msg.extra.copy() if msg.extra else {}

            # Check if message has file_keys and generate presigned URLs
            if msg.extra and msg.extra.get("file_keys"):
                file_keys = msg.extra["file_keys"]
                if isinstance(file_keys, list) and file_keys:
                    file_urls = await storage_service.generate_file_urls(file_keys)
                    enhanced_extra["file_urls"] = file_urls

            message_schema = MessageSchema(
                id=msg.id,
                sender=msg.sender,
                content=msg.content,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
                extra=enhanced_extra,
            )
            messages.append(message_schema)

        return ConversationSchema(
            id=conversation.id,
            title=conversation.title,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at,
            messages=messages,
            message_count=len(messages),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation",
        )


@router.put("/{conversation_id}", response_model=ConversationNoMessageSchema)
async def update_conversation(
    conversation_id: str = Path(..., description="Conversation ID"),
    request: ConversationUpdateRequest = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Update a conversation"""
    try:
        conversation_service = ConversationService(db)

        # Prepare update data (only include non-None values)
        update_data = {}
        if request.title is not None:
            update_data["title"] = request.title

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid fields to update",
            )

        updated_conversation = await conversation_service.update_conversation(
            conversation_id=conversation_id,
            user_id=str(current_user.id),
            update_data=update_data,
        )

        if not updated_conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
            )

        return ConversationNoMessageSchema(
            id=updated_conversation.id,
            title=updated_conversation.title,
            created_at=updated_conversation.created_at,
            updated_at=updated_conversation.updated_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update conversation",
        )


@router.delete("/{conversation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_conversation(
    conversation_id: str = Path(..., description="Conversation ID"),
    permanent: bool = Query(False, description="Permanently delete conversation"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Delete a conversation"""
    try:
        conversation_service = ConversationService(db)
        success = await conversation_service.delete_conversation(
            conversation_id=conversation_id,
            user_id=str(current_user.id),
            permanent=permanent,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation",
        )


# ============================================================================
# MESSAGE ENDPOINTS
# ============================================================================


@router.get("/{conversation_id}/messages", response_model=List[MessageSchema])
async def get_conversation_messages(
    conversation_id: str = Path(..., description="Conversation ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    limit: int = Query(50, ge=1, le=200, description="Number of messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    order: str = Query("asc", regex="^(asc|desc)$", description="Message order"),
):
    """Get messages for a conversation"""
    try:
        # Verify conversation access
        conversation_service = ConversationService(db)
        conversation = await conversation_service.get_conversation_by_id(
            conversation_id=conversation_id, user_id=str(current_user.id)
        )

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
            )

        # Get messages
        messages = await conversation_service.get_conversation_messages(
            conversation_id=conversation_id, limit=limit, offset=offset, order=order
        )

        return [
            MessageSchema(
                id=msg.id,
                sender=msg.sender,
                content=msg.content,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
            )
            for msg in messages
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get messages for conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve messages",
        )


# ============================================================================
# ANALYTICS ENDPOINTS
# ============================================================================


@router.get("/{conversation_id}/analytics", response_model=ConversationAnalyticsSchema)
async def get_conversation_analytics(
    conversation_id: str = Path(..., description="Conversation ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Get analytics for a conversation"""
    try:
        conversation_service = ConversationService(db)

        # Verify access
        conversation = await conversation_service.get_conversation_by_id(
            conversation_id=conversation_id, user_id=str(current_user.id)
        )

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found"
            )

        # Get analytics
        analytics = await conversation_service.get_conversation_analytics(
            conversation_id
        )

        return ConversationAnalyticsSchema(**analytics)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analytics for conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analytics",
        )


# ============================================================================
# BULK OPERATIONS
# ============================================================================


@router.post("/bulk/archive", response_model=BulkOperationResult)
async def bulk_archive_conversations(
    request: BulkOperationRequest = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Bulk archive conversations"""
    try:
        conversation_service = ConversationService(db)
        result = await conversation_service.bulk_archive_conversations(
            conversation_ids=request.conversation_ids, user_id=str(current_user.id)
        )

        return BulkOperationResult(
            successful=result["archived"],
            failed=result["failed"],
            total_processed=len(request.conversation_ids),
        )

    except Exception as e:
        logger.error(f"Failed to bulk archive conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to archive conversations",
        )


@router.post("/bulk/delete", response_model=BulkOperationResult)
async def bulk_delete_conversations(
    request: BulkOperationRequest = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Bulk delete conversations"""
    try:
        conversation_service = ConversationService(db)
        result = await conversation_service.bulk_delete_conversations(
            conversation_ids=request.conversation_ids,
            user_id=str(current_user.id),
            permanent=request.permanent,
        )

        return BulkOperationResult(
            successful=result["deleted"],
            failed=result["failed"],
            total_processed=len(request.conversation_ids),
        )

    except Exception as e:
        logger.error(f"Failed to bulk delete conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversations",
        )

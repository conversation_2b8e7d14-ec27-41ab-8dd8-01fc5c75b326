import json
import logging
import time

import svix
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from gentekai.api.schemas.webhook import (
    ClerkEventType,
    ClerkWebhookPayload,
    UserWebhookResponse,
)
from gentekai.config import settings
from gentekai.deps import get_db
from gentekai.services.user_service import UserService

logger = logging.getLogger(__name__)

webhook_router = APIRouter()


@webhook_router.post("/clerk/user", response_model=UserWebhookResponse)
async def handle_clerk_user_webhook(
    request: Request, db: AsyncSession = Depends(get_db)
):
    """
    Handle Clerk user webhooks (user.created, user.updated, user.deleted)

    This endpoint receives webhooks from Clerk when user events occur.
    It verifies the webhook signature and processes the user data accordingly.
    """
    start_time = time.time()

    try:
        payload = await request.body()
        # Verify signature
        wh = svix.Webhook(settings.CLERK_WEBHOOK_SECRET)
        try:
            wh.verify(payload, dict(request.headers))
        except Exception as e:
            print("Webhook verification failed:", e)
            raise HTTPException(status_code=400, detail="Webhook verification failed.")
        data_payload = json.loads(payload)

        # Parse webhook payload
        try:
            webhook_payload = ClerkWebhookPayload(**data_payload)
        except Exception as e:
            logger.error(f"Failed to parse webhook payload: {e}")
            raise HTTPException(
                status_code=400, detail="Failed to parse webhook payload."
            )

        # Extract user ID for logging
        clerk_user_id = webhook_payload.data.id
        event_type = webhook_payload.type

        # Log webhook received
        logger.debug(
            "Validating webhook signature",
            event_type.value,
            clerk_user_id,
        )

        # Initialize user service
        user_service = UserService(db)

        # Process webhook based on event type
        response = await process_user_webhook(user_service, webhook_payload)

        # Calculate processing time
        processing_time_ms = int((time.time() - start_time) * 1000)

        # Add processing metadata to response
        if response.data is None:
            response.data = {}
        response.data.update(
            {
                "processing_time_ms": processing_time_ms,
            }
        )

        return response

    except HTTPException:
        # Re-raise HTTP exceptions (they're already properly formatted)
        raise
    except Exception as e:
        # Handle unexpected errors
        processing_time_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Unexpected webhook error: {e}", exc_info=True)

        return UserWebhookResponse(
            success=False,
            message="Internal server error",
            error=str(e),
            action="error",
            data={"processing_time_ms": processing_time_ms},
        )


async def process_user_webhook(
    user_service: UserService, webhook_payload: ClerkWebhookPayload
) -> UserWebhookResponse:
    """Process user webhook based on event type"""

    event_type = webhook_payload.type
    clerk_user_id = webhook_payload.data.id

    try:
        if event_type == ClerkEventType.USER_CREATED:
            return await handle_user_created(user_service, webhook_payload)

        elif event_type == ClerkEventType.USER_UPDATED:
            return await handle_user_updated(user_service, webhook_payload)

        elif event_type == ClerkEventType.USER_DELETED:
            return await handle_user_deleted(user_service, webhook_payload)

        else:
            logger.warning(f"Unsupported event type: {event_type}")
            return UserWebhookResponse(
                success=False,
                message=f"Unsupported event type: {event_type}",
                clerk_user_id=clerk_user_id,
                action="unsupported",
                error="UNSUPPORTED_EVENT_TYPE",
            )

    except Exception as e:
        logger.error(f"Failed to process {event_type} for user {clerk_user_id}: {e}")
        return UserWebhookResponse(
            success=False,
            message=f"Failed to process {event_type.value}",
            clerk_user_id=clerk_user_id,
            action=event_type.value.split(".")[1],  # Extract action part
            error=str(e),
        )


async def handle_user_created(
    user_service: UserService, webhook_payload: ClerkWebhookPayload
) -> UserWebhookResponse:
    """Handle user.created webhook event"""

    clerk_user_id = webhook_payload.data.id

    try:
        # Create user from Clerk data
        user, created = await user_service.create_user_from_clerk(
            webhook_payload.dict()
        )

        if created:
            logger.info(
                f"Successfully created user {user.id} from Clerk ID {clerk_user_id}"
            )
            return UserWebhookResponse(
                success=True,
                message="User created successfully",
                user_id=user.id,
                clerk_user_id=clerk_user_id,
                action="created",
                data={
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "role": user.role.value if user.role else None,
                },
            )
        else:
            logger.info(f"User {user.id} already exists for Clerk ID {clerk_user_id}")
            return UserWebhookResponse(
                success=True,
                message="User already exists",
                user_id=user.id,
                clerk_user_id=clerk_user_id,
                action="exists",
                data={
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                },
            )

    except ValueError as e:
        # Handle validation errors (duplicate email, etc.)
        logger.warning(f"Validation error creating user {clerk_user_id}: {e}")
        return UserWebhookResponse(
            success=False,
            message="User creation failed due to validation error",
            clerk_user_id=clerk_user_id,
            action="created",
            error=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to create user {clerk_user_id}: {e}")
        raise


async def handle_user_updated(
    user_service: UserService, webhook_payload: ClerkWebhookPayload
) -> UserWebhookResponse:
    """Handle user.updated webhook event"""

    clerk_user_id = webhook_payload.data.id

    try:
        # Update user from Clerk data
        updated_user = await user_service.update_user_from_clerk(webhook_payload.dict())

        if updated_user:
            logger.info(
                f"Successfully updated user {updated_user.id} from Clerk ID {clerk_user_id}"
            )
            return UserWebhookResponse(
                success=True,
                message="User updated successfully",
                user_id=updated_user.id,
                clerk_user_id=clerk_user_id,
                action="updated",
                data={
                    "email": updated_user.email,
                    "first_name": updated_user.first_name,
                    "last_name": updated_user.last_name,
                    "role": updated_user.role.value if updated_user.role else None,
                },
            )
        else:
            logger.warning(f"User not found for Clerk ID {clerk_user_id} during update")
            return UserWebhookResponse(
                success=False,
                message="User not found for update",
                clerk_user_id=clerk_user_id,
                action="updated",
                error="USER_NOT_FOUND",
            )

    except Exception as e:
        logger.error(f"Failed to update user {clerk_user_id}: {e}")
        raise


async def handle_user_deleted(
    user_service: UserService, webhook_payload: ClerkWebhookPayload
) -> UserWebhookResponse:
    """Handle user.deleted webhook event"""

    clerk_user_id = webhook_payload.data.id

    try:
        # Delete user
        success = await user_service.delete_user_from_clerk(clerk_user_id)

        if success:
            logger.info(f"Successfully deleted user with Clerk ID {clerk_user_id}")
            return UserWebhookResponse(
                success=True,
                message="User deleted successfully",
                clerk_user_id=clerk_user_id,
                action="deleted",
            )
        else:
            logger.warning(
                f"User not found for Clerk ID {clerk_user_id} during deletion"
            )
            return UserWebhookResponse(
                success=False,
                message="User not found for deletion",
                clerk_user_id=clerk_user_id,
                action="deleted",
                error="USER_NOT_FOUND",
            )

    except Exception as e:
        logger.error(f"Failed to delete user {clerk_user_id}: {e}")
        raise

import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from starlette import status

from gentekai.api.schemas.upload import PresignedUrlRequest, PresignedUrlResponse
from gentekai.config import settings
from gentekai.db.models import User
from gentekai.deps import get_current_user
from gentekai.services.file_service import FileValidationService
from gentekai.storage.r2 import R2StorageService
from gentekai.utils.file_utils import generate_file_key

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/presigned-url", response_model=PresignedUrlResponse)
async def get_presigned_url(
    request: PresignedUrlRequest,
    current_user: User = Depends(get_current_user),
):
    """Generate a presigned URL for direct upload to storage"""

    try:
        # Validate the file
        file_service = FileValidationService()
        file_extension = file_service.validate_file(
            request.filename, request.content_type, request.file_size
        )

        # Generate unique file key
        file_key = generate_file_key(request.filename, file_extension)

        # Get storage service and generate presigned URL
        storage_service = R2StorageService()
        presigned_url = storage_service.generate_presigned_url(
            file_key=file_key,
            content_type=request.content_type,
            file_size=request.file_size,
        )

        # Get public URL
        public_url = storage_service.get_public_url(file_key)

        logger.info(
            f"Generated presigned URL for file: {request.filename} -> {file_key}"
        )
        # storage_service.test_bucket_access()

        return PresignedUrlResponse(
            presigned_url=presigned_url,
            file_key=file_key,
            upload_url=public_url,
            expires_in=settings.PRESIGNED_URL_EXPIRY,
        )

    except Exception as e:
        logger.error(f"Error generating presigned URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate presigned URL",
        )

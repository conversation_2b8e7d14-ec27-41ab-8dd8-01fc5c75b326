from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


# ============================================================================
# CLERK WEBHOOK SCHEMAS
# ============================================================================
class ClerkEventType(str, Enum):
    """Clerk webhook event types"""

    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    USER_DELETED = "user.deleted"
    SESSION_CREATED = "session.created"
    SESSION_ENDED = "session.ended"
    EMAIL_CREATED = "email.created"
    SMS_CREATED = "sms.created"


class ClerkEmailAddress(BaseModel):
    """Clerk email address object"""

    id: str
    email_address: str
    verification: Optional[Dict[str, Any]] = None
    linked_to: Optional[List[Dict[str, Any]]] = None


class ClerkExternalAccount(BaseModel):
    """Clerk external account object"""

    id: str
    provider: str
    identification_id: str
    provider_user_id: str
    approved_scopes: List[str] = []
    email_address: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    image_url: Optional[str] = None

    @validator("approved_scopes", pre=True)
    def parse_approved_scopes(cls, v):
        """Convert space-separated string to list if needed"""
        if isinstance(v, str):
            # Split by space and filter out empty strings
            return [scope.strip() for scope in v.split() if scope.strip()]
        elif isinstance(v, list):
            return v
        else:
            return []


class ClerkUserData(BaseModel):
    """Clerk user data object"""

    id: str
    object: str = "user"
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    image_url: Optional[str] = None
    has_image: bool = False
    primary_email_address_id: Optional[str] = None
    primary_phone_number_id: Optional[str] = None
    primary_web3_wallet_id: Optional[str] = None
    password_enabled: bool = False
    two_factor_enabled: bool = False
    totp_enabled: bool = False
    backup_code_enabled: bool = False
    email_addresses: List[ClerkEmailAddress] = []
    phone_numbers: List[Dict[str, Any]] = []
    web3_wallets: List[Dict[str, Any]] = []
    external_accounts: List[ClerkExternalAccount] = []
    public_metadata: Dict[str, Any] = {}
    private_metadata: Dict[str, Any] = {}
    unsafe_metadata: Dict[str, Any] = {}
    external_id: Optional[str] = None
    created_at: int  # Unix timestamp
    updated_at: int  # Unix timestamp
    banned: bool = False
    locked: bool = False
    lockout_expires_in_seconds: Optional[int] = None
    verification_attempts_remaining: int = 0


class ClerkWebhookPayload(BaseModel):
    """Clerk webhook payload"""

    data: ClerkUserData
    object: str
    type: ClerkEventType

    @validator("type")
    def validate_event_type(cls, v):
        if v not in ClerkEventType.__members__.values():
            raise ValueError(f"Unsupported event type: {v}")
        return v


# ============================================================================
# WEBHOOK RESPONSE SCHEMAS
# ============================================================================
class WebhookResponse(BaseModel):
    """Standard webhook response"""

    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class UserWebhookResponse(WebhookResponse):
    """User webhook specific response"""

    user_id: Optional[UUID] = None
    clerk_user_id: Optional[str] = None
    action: str  # "created", "updated", "deleted"

    # Example responses:
    """
    Success Response:
    {
        "success": true,
        "message": "User created successfully",
        "data": {
            "user_id": "550e8400-e29b-41d4-a716-************",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe"
        },
        "user_id": "550e8400-e29b-41d4-a716-************",
        "clerk_user_id": "user_2NiSUXmpthJcHdKKLOp7FJJQKq7",
        "action": "created",
        "timestamp": "2025-05-27T12:00:00.000Z"
    }

    Error Response:
    {
        "success": false,
        "message": "Failed to create user",
        "error": "User with this email already exists",
        "user_id": null,
        "clerk_user_id": "user_2NiSUXmpthJcHdKKLOp7FJJQKq7",
        "action": "created",
        "timestamp": "2025-05-27T12:00:00.000Z"
    }
    """


# ============================================================================
# WEBHOOK VALIDATION SCHEMAS
# ============================================================================
class WebhookHeaders(BaseModel):
    """Clerk webhook headers for validation"""

    svix_id: str = Field(..., alias="svix-id")
    svix_timestamp: str = Field(..., alias="svix-timestamp")
    svix_signature: str = Field(..., alias="svix-signature")

    class Config:
        validate_by_name = True


# ============================================================================
# USER CREATION SCHEMAS (for internal use)
# ============================================================================
class UserCreateFromWebhook(BaseModel):
    """User creation data from webhook"""

    clerk_user_id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile: Optional[Dict[str, Any]] = None


class UserUpdateFromWebhook(BaseModel):
    """User update data from webhook"""

    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile: Optional[Dict[str, Any]] = None


# ============================================================================
# WEBHOOK EVENT LOG SCHEMA (for logging/monitoring)
# ============================================================================
class WebhookEventLog(BaseModel):
    """Webhook event log for monitoring"""

    id: UUID
    event_type: ClerkEventType
    clerk_user_id: str
    success: bool
    error_message: Optional[str] = None
    processing_time_ms: int
    created_at: datetime
    payload_size_bytes: int
    user_agent: Optional[str] = None

    # Example log entry:
    """
    {
        "id": "550e8400-e29b-41d4-a716-************",
        "event_type": "user.created",
        "clerk_user_id": "user_2NiSUXmpthJcHdKKLOp7FJJQKq7",
        "success": true,
        "error_message": null,
        "processing_time_ms": 156,
        "created_at": "2025-05-27T12:00:00.000Z",
        "payload_size_bytes": 2048,
        "user_agent": "Svix/1.4 (https://github.com/svix/svix-webhooks)"
    }
    """


# ============================================================================
# BULK WEBHOOK OPERATIONS (for batch processing)
# ============================================================================
class BulkWebhookPayload(BaseModel):
    """Bulk webhook payload for processing multiple events"""

    events: List[ClerkWebhookPayload]
    batch_id: str
    total_events: int

    @validator("events")
    def validate_events_not_empty(cls, v):
        if not v:
            raise ValueError("Events list cannot be empty")
        return v

    @validator("total_events")
    def validate_total_matches_events(cls, v, values):
        if "events" in values and len(values["events"]) != v:
            raise ValueError("total_events must match the length of events list")
        return v


class BulkWebhookResponse(BaseModel):
    """Bulk webhook processing response"""

    batch_id: str
    total_events: int
    successful_events: int
    failed_events: int
    results: List[UserWebhookResponse]
    processing_time_ms: int
    errors: List[Dict[str, Any]] = []

    # Example bulk response:
    """
    {
        "batch_id": "batch_2025052712000001",
        "total_events": 5,
        "successful_events": 4,
        "failed_events": 1,
        "results": [
            {
                "success": true,
                "message": "User created successfully",
                "user_id": "550e8400-e29b-41d4-a716-************",
                "clerk_user_id": "user_2NiSUXmpthJcHdKKLOp7FJJQKq7",
                "action": "created"
            }
        ],
        "processing_time_ms": 2450,
        "errors": [
            {
                "event_index": 2,
                "clerk_user_id": "user_invalid",
                "error": "Invalid user data format"
            }
        ]
    }
    """

from typing import Literal, Optional, Union, List
from pydantic import BaseModel


# File object schema
class SlackFile(BaseModel):
    id: str
    name: str
    title: Optional[str] = None
    mimetype: str
    filetype: str
    size: int
    url_private: str
    url_private_download: str
    permalink: str
    permalink_public: Optional[str] = None
    thumb_64: Optional[str] = None
    thumb_80: Optional[str] = None
    thumb_160: Optional[str] = None
    thumb_360: Optional[str] = None
    thumb_480: Optional[str] = None
    thumb_720: Optional[str] = None
    is_external: bool = False
    external_type: Optional[str] = None


# File shared event
class SlackFileSharedEvent(BaseModel):
    type: Literal["file_shared"]
    channel: Optional[str] = None  # Can be None for private files
    user: str
    file_id: str
    ts: str


# File created event
class SlackFileCreatedEvent(BaseModel):
    type: Literal["file_created"]
    user: str
    file_id: str
    ts: str


# Updated message event with files support
class SlackMessageEvent(BaseModel):
    type: Literal["message"]
    channel: str
    user: Optional[str] = None
    text: Optional[str] = None
    ts: str
    thread_ts: Optional[str] = None
    bot_id: Optional[str] = None
    subtype: Optional[str] = None
    files: Optional[List[SlackFile]] = None  # Files attached to message
    upload: Optional[bool] = None  # True if this is a file upload message


class SlackAppMentionEvent(BaseModel):
    type: Literal["app_mention"]
    channel: str
    user: str
    text: str
    ts: str
    thread_ts: Optional[str] = None
    files: Optional[List[SlackFile]] = None  # Files in mention


class SlackEvent(BaseModel):
    type: str
    channel: Optional[str] = None
    user: Optional[str] = None
    text: Optional[str] = None
    ts: Optional[str] = None
    thread_ts: Optional[str] = None
    channel_type: Optional[Literal["channel", "group", "im", "mpim"]] = None
    files: Optional[List[SlackFile]] = None
    file_id: Optional[str] = None  # For file_shared/file_created events
    upload: Optional[bool] = None

    class Config:
        extra = "allow"

    def get(self, key: str, default=None):
        """Dictionary-like get method"""
        return getattr(self, key, default)


# Keep your existing schemas...
class SlackUrlVerificationRequest(BaseModel):
    token: str
    challenge: str
    type: Literal["url_verification"]


class SlackEventCallbackRequest(BaseModel):
    token: str
    team_id: str
    api_app_id: str
    event: SlackEvent
    type: Literal["event_callback"]
    event_id: str
    event_time: int


SlackRequest = Union[SlackUrlVerificationRequest, SlackEventCallbackRequest]
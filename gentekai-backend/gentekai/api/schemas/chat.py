from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class ChatStreamRequest(BaseModel):
    prompt: str
    conversation_id: str
    file_keys: List[str] | None = None

    # Context processing options
    context_strategy: Optional[str] = (
        "auto"  # auto, full_text, embeddings, summary, hybrid
    )
    max_context_length: Optional[int] = 16000
    include_file_analysis: Optional[bool] = True


class DocumentContextResponse(BaseModel):
    context: str
    files_processed: List[Dict[str, Any]]
    total_context_length: int
    strategy_used: str

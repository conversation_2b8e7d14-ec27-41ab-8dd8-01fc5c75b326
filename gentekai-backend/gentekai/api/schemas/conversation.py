from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


# ============================================================================
# MESSAGE SCHEMAS
# ============================================================================
class MessageSchema(BaseModel):
    """Message response schema"""

    id: UUID
    sender: str
    content: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    extra: Optional[dict] = None

    class Config:
        from_attributes = True


class MessageCreateRequest(BaseModel):
    """Message creation request"""

    content: str = Field(..., min_length=1, max_length=10000)


# ============================================================================
# CONVERSATION SCHEMAS
# ============================================================================
class ConversationBaseSchema(BaseModel):
    """Base conversation schema"""

    id: UUID
    title: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ConversationNoMessageSchema(ConversationBaseSchema):
    """Conversation without messages"""

    message_count: int = 0
    last_message_at: Optional[datetime] = None
    last_message_preview: Optional[str] = None


class ConversationSchema(ConversationBaseSchema):
    """Full conversation with messages"""

    messages: List[MessageSchema] = []
    message_count: int = 0


class ConversationListItemSchema(ConversationBaseSchema):
    """Conversation list item with summary info"""

    message_count: int = 0
    last_message_at: Optional[datetime] = None
    last_message_preview: Optional[str] = None
    is_archived: bool = False


class ConversationListResponse(BaseModel):
    """Paginated conversation list response"""

    conversations: List[ConversationListItemSchema]
    total: int
    limit: int
    offset: int
    has_next: bool


# ============================================================================
# REQUEST SCHEMAS
# ============================================================================
class ConversationCreateRequest(BaseModel):
    """Conversation creation request"""

    title: Optional[str] = Field(None, max_length=200)
    initial_message: Optional[str] = Field(None, max_length=10000)


class ConversationUpdateRequest(BaseModel):
    """Conversation update request"""

    title: Optional[str] = Field(None, max_length=200)


class BulkOperationRequest(BaseModel):
    """Bulk operation request"""

    conversation_ids: List[str] = Field(..., min_items=1, max_items=50)
    permanent: bool = False


# ============================================================================
# ANALYTICS SCHEMAS
# ============================================================================
class ConversationAnalyticsSchema(BaseModel):
    """Conversation analytics response"""

    conversation_id: str
    total_messages: int
    user_messages: int
    assistant_messages: int
    first_message_at: Optional[datetime]
    last_message_at: Optional[datetime]
    average_message_length: float
    total_characters: int


# ============================================================================
# OPERATION RESULT SCHEMAS
# ============================================================================
class BulkOperationResult(BaseModel):
    """Bulk operation result"""

    successful: List[str]
    failed: List[dict]
    total_processed: int


class HealthCheckResponse(BaseModel):
    """Health check response"""

    service: str
    status: str
    database: Optional[str] = None
    total_conversations: Optional[int] = None
    total_messages: Optional[int] = None
    error: Optional[str] = None
    timestamp: str

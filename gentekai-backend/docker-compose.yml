services:
  backend:
    container_name: gtk_backend
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
    ports:
      - 9876:8000
    # Overrides default command so things don't shut down after the process ends.
    command: while sleep 1000; do :; done
    env_file:
      - ./.env.secrets

  db_mcp_server:
    container_name: gtk_db_mcp_server
    build:
      context: ../gentekai-sql-mcp-server
      dockerfile: Dockerfile
    ports:
      - 9877:3002
    environment:
      PORT: 3002
      DATABASE_URL: **************************************/dev_db
    # Overrides default command so things don't shut down after the process ends.
    command: while sleep 1000; do :; done
    depends_on:
      - db

  db:
    image: pgvector/pgvector:pg17
    container_name: gtk_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: dev_db
    ports:
      - 6543:5432
    volumes:
      - genai_db:/var/lib/postgresql/data

volumes:
  genai_db:

# GenTekai Multi-Agent Platform

## Overview

GenTekai is a multi-agent platform that uses AI to help with tasks such as:

- HR
- Recruitment
- Onboarding
- Employee Management
- and more

## Development

### Setup

1. Clone the repository
2. Install dependencies:

```bash
uv sync
```

3. Run the development server

```bash
uv run uvicorn gentekai.main:app --reload
```

### Lint and format

```bash
uv run ruff check --fix .
uv run ruff format .
```

## Deployment

### Migrations

- Migrations are handled by `gentekai-sql-mcp-server` service.

### Docker

- Copy `.env.example` to `.env.secrets` and fill in the required environment variables.
- Database is persisted in `genai_db` volume.
- `docker compose up` will start the services.

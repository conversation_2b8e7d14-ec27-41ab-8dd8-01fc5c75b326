// app/utils/auth.server.ts
import { getAuth } from "@clerk/remix/ssr.server";
import { redirect } from "@remix-run/node";

// You can accept args (from loader or action) or just { request }
export async function requireClerkAuth(args: any) {
  const { userId, getToken } = await getAuth(args as any);
  if (!userId) {
    console.error("No userId found");
    // Redirect to sign-in page if user is not authenticated
    throw redirect("/sign-in");
  }
  const token = await getToken();
  if (!token) {
    console.error("No token found");
    throw new Response("No valid session", { status: 401 });
  }
  return { userId, token };
}

"use client";

import { useEffect, useRef } from "react";
import { ChatMessage, ChatTypingIndicator } from "./ChatMessage";
import { Skeleton } from "./ui/skeleton";

interface ChatMessageListProps {
  messages: Message[];
  isLoading: boolean;
  isStreaming: boolean;
  streamedText?: string;
}

export function ChatMessageList({
  messages,
  isLoading,
  isStreaming,
}: ChatMessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change or when loading/streaming state changes
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading, isStreaming]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  if (isLoading) {
    return (
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-16 w-3/4" />
          <Skeleton className="h-16 w-1/2 ml-auto" />
          <Skeleton className="h-32 w-3/4" />
          <Skeleton className="h-16 w-1/2 ml-auto" />
          <Skeleton className="h-24 w-3/4" />
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 overflow-y-auto p-4 flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <p>No messages yet. Start a conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-6">
      {messages.map((message) => (
        <ChatMessage
          key={message.id}
          content={message.content}
          sender={message.sender}
          extra={message.extra}
          timestamp={new Date(
            message.timestamp ?? new Date().toString()
          ).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
        />
      ))}
      {isStreaming && <ChatTypingIndicator />}
      <div ref={messagesEndRef} />
    </div>
  );
}

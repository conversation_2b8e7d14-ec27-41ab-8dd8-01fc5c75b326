"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { UserButton } from "@clerk/remix";
import { NavLink, useNavigate } from "@remix-run/react";
import {
  LaptopIcon,
  MessageSquare,
  MoonIcon,
  Plus,
  SunIcon,
  X,
} from "lucide-react";
import { useCallback, useState } from "react";
import { useHydrated } from "remix-utils/use-hydrated";
import { getTheme, setTheme as setSystemTheme } from "./theme-switcher";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

interface ConversationListProps {
  conversations: Conversation[];
  sidebarOpen: boolean;
  onToggleSidebar: () => void;
  isLoading: boolean;
  activeConversationId?: string;
}

export function ConversationList({
  conversations,
  sidebarOpen,
  onToggleSidebar,
  isLoading,
}: ConversationListProps) {
  const navigate = useNavigate();

  const hydrated = useHydrated();
  const [, rerender] = useState({});
  const setTheme = useCallback((theme: string) => {
    setSystemTheme(theme);
    rerender({});
  }, []);
  const theme = getTheme();

  // Format the date to a readable string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays === 0) {
      return "Today";
    }
    if (diffInDays === 1) {
      return "Yesterday";
    }
    if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    }
    return date.toLocaleDateString();
  };

  // Group conversations by date
  const groupedConversations = conversations.reduce<
    Record<string, Conversation[]>
  >((groups, conversation) => {
    const date = formatDate(
      conversation.updated_at ?? conversation.created_at ?? ""
    );
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(conversation);
    return groups;
  }, {});

  return (
    <div
      className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-secondary transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0 flex flex-col",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}
    >
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-xl font-bold">GenTekAI</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleSidebar}
          className="md:hidden"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="p-4">
        <Button
          className="w-full mb-4 justify-start"
          type="button"
          onClick={() => navigate("/")}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Chat
        </Button>
      </div>

      {/* Conversation list with flex-1 to allow it to scroll */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {isLoading ? (
          // Loading skeletons
          <div className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        ) : (
          // Actual conversations grouped by date
          <div className="space-y-4">
            {Object.entries(groupedConversations).map(([date, convos]) => (
              <div key={date} className="space-y-2">
                <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  {date}
                </h3>
                {convos.map((conversation) => (
                  <NavLink
                    prefetch="intent"
                    key={conversation.id}
                    to={`/chat/${conversation.id}`}
                    end
                    className={({ isActive }) => {
                      return cn(
                        "flex items-center gap-2 p-2 rounded-md cursor-pointer hover:bg-accent text-sm",
                        isActive &&
                          "bg-accent text-accent-foreground dark:bg-accent-foreground"
                      );
                    }}
                  >
                    <MessageSquare className="h-4 w-4 flex-shrink-0" />
                    <div className="truncate flex-1">
                      <div className="font-medium truncate">
                        {conversation.title}
                      </div>
                      <div className="text-xs text-muted-foreground truncate" />
                    </div>
                  </NavLink>
                ))}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Settings and theme toggle at the bottom */}
      <div className="p-4 border-t mt-auto">
        <div className="flex items-center justify-between">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className="w-10 h-10 rounded-full border"
                size="icon"
                variant="ghost"
              >
                <span className="sr-only">Theme selector</span>
                {!hydrated ? null : theme === "dark" ? (
                  <MoonIcon />
                ) : theme === "light" ? (
                  <SunIcon />
                ) : (
                  <LaptopIcon />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="mt-2">
              <DropdownMenuLabel>Theme</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <button
                  type="button"
                  className="w-full"
                  onClick={() => setTheme("light")}
                  aria-selected={theme === "light"}
                >
                  Light
                </button>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <button
                  type="button"
                  className="w-full"
                  onClick={() => setTheme("dark")}
                  aria-selected={theme === "dark"}
                >
                  Dark
                </button>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <button
                  type="button"
                  className="w-full"
                  onClick={() => setTheme("system")}
                  aria-selected={theme === "system"}
                >
                  System
                </button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="ghost" size="icon" title="Settings">
            <UserButton />
          </Button>
        </div>
      </div>
    </div>
  );
}

import { useRef } from "react";
import { ChatInput, ChatInputRef } from "./ChatInput";

export default function ChatInitiator({
  onSendMessage,
}: {
  onSendMessage: (content: string) => void;
}) {
  const chatInputRef = useRef<ChatInputRef>(null);

  return (
    <div className="h-full flex items-center justify-center p-4">
      <div className="max-w-md text-center">
        <h2 className="text-2xl font-bold mb-4">Welcome to GenTekAI</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Need help with something? Start a new conversation or select an
          existing one.
        </p>
        <ChatInput
          ref={chatInputRef}
          isSubmitting={false}
          onSendMessage={onSendMessage}
          disabled={false}
        />
      </div>
    </div>
  );
}

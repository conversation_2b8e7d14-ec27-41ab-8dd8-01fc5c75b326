"use client";

import type React from "react";

import { FilePreview } from "@/components/FilePreview";
import { Button } from "@/components/ui/button";
import { uploadToR2, validateFile, type UploadedFile } from "@/lib/upload";
import { cn } from "@/lib/utils";
import { Paperclip, Send } from "lucide-react";
import {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  type DragEvent,
  type KeyboardEvent,
} from "react";

interface ChatInputProps {
  isSubmitting: boolean;
  disabled?: boolean;
  className?: string;
  onSendMessage: (message: string, files?: UploadedFile[]) => void;
}

export interface ChatInputRef {
  focus: () => void;
}

export const ChatInput = forwardRef<ChatInputRef, ChatInputProps>(
  ({ isSubmitting, disabled, className, onSendMessage }, ref) => {
    const [message, setMessage] = useState("");
    const [isDragOver, setIsDragOver] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
    const [uploadError, setUploadError] = useState<string | null>(null);

    // Expose focus method to parent components
    useImperativeHandle(ref, () => ({
      focus: () => {
        textareaRef.current?.focus();
      },
    }));

    const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
      // Submit on Enter (but not with Shift+Enter)
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        if (
          (message.trim() || uploadedFiles.length > 0) &&
          !isSubmitting &&
          !disabled
        ) {
          handleSendMessage();
        }
      }
    };

    const handleFileSelect = async (files: FileList | null) => {
      if (!files) return;

      const newFiles = Array.from(files);
      const validFiles: File[] = [];
      let hasError = false;

      // Validate files first
      for (const file of newFiles) {
        const validation = validateFile(file);
        if (validation.valid) {
          validFiles.push(file);
        } else {
          setUploadError(validation.error || "Invalid file");
          hasError = true;
          break;
        }
      }

      if (hasError || validFiles.length === 0) return;

      setUploadError(null);
      setIsUploading(true);

      try {
        // Upload files immediately
        const uploadPromises = validFiles.map(uploadToR2);
        const uploadedFileData = await Promise.all(uploadPromises);

        // Add to uploaded files list
        setUploadedFiles((prev) => [...prev, ...uploadedFileData]);
      } catch (error) {
        setUploadError(
          error instanceof Error ? error.message : "Upload failed"
        );
      } finally {
        setIsUploading(false);
      }
    };

    const handleSendMessage = async () => {
      if (
        (!message.trim() && uploadedFiles.length === 0) ||
        isSubmitting ||
        disabled
      )
        return;

      try {
        onSendMessage(
          message,
          uploadedFiles.length > 0 ? uploadedFiles : undefined
        );
        setMessage("");
        setUploadedFiles([]);

        // Reset textarea height
        if (textareaRef.current) {
          textareaRef.current.style.height = "auto";
        }
      } catch (error) {
        setUploadError(
          error instanceof Error ? error.message : "Failed to send message"
        );
      }
    };

    const handleInput = () => {
      const textarea = textareaRef.current;
      if (textarea) {
        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = "auto";
        // Set the height to the scrollHeight to fit the content
        textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
      }
    };

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      handleFileSelect(e.target.files);
      // Reset the input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    };

    const removeFile = (index: number) => {
      setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
    };

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      setIsDragOver(true);
    };

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      handleFileSelect(e.dataTransfer.files);
    };

    const formatFileSize = (bytes: number | undefined) => {
      if (!bytes || bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (
        Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
      );
    };

    return (
      <div className={cn("p-4 border-t", className)}>
        {uploadError && (
          <div className="mb-3 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{uploadError}</p>
          </div>
        )}
        {/* File previews */}
        {uploadedFiles.length > 0 && (
          <div className="mb-3 flex flex-wrap gap-2">
            {uploadedFiles.map((file, index) => (
              <FilePreview
                key={index}
                file={file}
                onRemove={() => removeFile(index)}
              />
            ))}
          </div>
        )}

        <div
          className={cn(
            "flex items-start space-x-2 rounded-lg transition-colors",
            isDragOver && "bg-muted/50 border-2 border-dashed border-primary"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="relative flex-1 min-h-[40px]">
            <textarea
              ref={textareaRef}
              placeholder={isDragOver ? "Drop files here..." : "Message AI..."}
              className="resize-none w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pr-10 min-h-[40px] max-h-[200px] overflow-y-auto"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onInput={handleInput}
              rows={1}
              disabled={isSubmitting || disabled || isUploading}
              aria-label="Chat message"
            />
          </div>
          <div className="flex space-x-2">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileInputChange}
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
            />
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="self-end"
              disabled={isSubmitting || disabled || isUploading}
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="h-5 w-5" />
              <span className="sr-only">Upload file</span>
            </Button>
            <Button
              type="button"
              onClick={handleSendMessage}
              disabled={
                isSubmitting ||
                disabled ||
                isUploading ||
                (!message.trim() && uploadedFiles.length === 0)
              }
              className="self-end"
            >
              {isSubmitting || isUploading ? (
                "Sending..."
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

ChatInput.displayName = "ChatInput";

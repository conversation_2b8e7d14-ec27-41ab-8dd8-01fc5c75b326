// components/FilePreview.tsx
import { cn } from "@/lib/utils"; // Adjust import based on your utils
import { useState } from "react";

interface FilePreviewProps {
  fileUrls?: FileUrl[];
  className?: string;
}

export function FilePreviewFetch({ fileUrls, className }: FilePreviewProps) {
  if (!fileUrls || fileUrls.length === 0) return null;

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {fileUrls.map((fileData, index) => (
        <FileItem key={`${fileData.file_key}-${index}`} fileData={fileData} />
      ))}
    </div>
  );
}

interface FileItemProps {
  fileData: FileUrl;
}

function FileItem({ fileData }: FileItemProps) {
  const { file_key, url, error } = fileData;
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Determine file type
  const fileExtension = file_key.split(".").pop()?.toLowerCase() || "";
  const isImage = ["jpg", "jpeg", "png", "gif", "webp", "svg", "bmp"].includes(
    fileExtension
  );
  const isPdf = fileExtension === "pdf";
  const isVideo = ["mp4", "webm", "ogg", "mov", "avi"].includes(fileExtension);
  const isAudio = ["mp3", "wav", "ogg", "aac", "m4a"].includes(fileExtension);

  // Extract filename from file_key (remove path if present)
  const fileName = file_key.split("/").pop() || file_key;

  if (error || !url) {
    return (
      <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg max-w-xs">
        <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded flex items-center justify-center">
          <span className="text-red-600 text-sm">⚠️</span>
        </div>
        <div className="min-w-0 flex-1">
          <div className="text-sm font-medium text-red-800 truncate">
            {fileName}
          </div>
          <div className="text-xs text-red-600">Failed to load</div>
        </div>
      </div>
    );
  }

  if (isImage && !imageError) {
    return (
      <div className="relative group max-w-sm">
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 rounded-lg animate-pulse flex items-center justify-center">
            <span className="text-xs text-gray-500">Loading...</span>
          </div>
        )}
        <img
          src={url}
          alt={fileName}
          className={cn(
            "max-w-full max-h-64 rounded-lg object-cover border border-gray-200 shadow-sm",
            "hover:shadow-md transition-shadow cursor-pointer",
            imageLoading && "opacity-0"
          )}
          onLoad={() => setImageLoading(false)}
          onError={() => {
            setImageError(true);
            setImageLoading(false);
          }}
          onClick={() => window.open(url, "_blank")}
        />
        {!imageLoading && (
          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="truncate">{fileName}</div>
          </div>
        )}
      </div>
    );
  }

  if (isVideo) {
    return (
      <div className="max-w-sm">
        <video
          controls
          className="max-w-full max-h-64 rounded-lg border border-gray-200 shadow-sm"
          preload="metadata"
        >
          <source src={url} />
          Your browser does not support the video tag.
        </video>
        <div className="text-xs text-gray-500 mt-1 truncate">{fileName}</div>
      </div>
    );
  }

  if (isAudio) {
    return (
      <div className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg max-w-xs">
        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
          <span className="text-blue-600 text-sm">🎵</span>
        </div>
        <div className="min-w-0 flex-1">
          <div className="text-sm font-medium text-gray-900 truncate">
            {fileName}
          </div>
          <audio controls className="w-full mt-2" preload="metadata">
            <source src={url} />
            Your browser does not support the audio tag.
          </audio>
        </div>
      </div>
    );
  }

  // Default file display (documents, PDFs, etc.)
  const getFileIcon = () => {
    if (isPdf) return "📄";
    if (["doc", "docx"].includes(fileExtension)) return "📝";
    if (["xls", "xlsx"].includes(fileExtension)) return "📊";
    if (["zip", "rar", "7z"].includes(fileExtension)) return "📦";
    if (["txt"].includes(fileExtension)) return "📋";
    return "📎";
  };

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors max-w-xs group"
    >
      <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded flex items-center justify-center group-hover:bg-gray-300 transition-colors">
        <span className="text-sm">{getFileIcon()}</span>
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-sm font-medium text-gray-900 truncate">
          {fileName}
        </div>
        <div className="text-xs text-gray-500">Click to open</div>
      </div>
      <div className="flex-shrink-0">
        <span className="text-gray-400 text-sm">↗</span>
      </div>
    </a>
  );
}

"use client";

import { Button } from "@/components/ui/button";
import type { UploadedFile } from "@/lib/upload";
import { X } from "lucide-react";

interface FilePreviewProps {
  file: UploadedFile;
  onRemove: () => void;
}

export function FilePreview({ file, onRemove }: FilePreviewProps) {
  const formatFileSize = (bytes: number | undefined) => {
    if (!bytes || bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (
      Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
    );
  };

  const isImage = file.type?.startsWith("image/");

  return (
    <div className="flex items-center gap-2 bg-muted rounded-md px-3 py-2 text-sm">
      {isImage && file.url && (
        <div className="w-8 h-8 rounded overflow-hidden flex-shrink-0">
          <img
            src={file.url || "/placeholder.svg"}
            alt={file.originalName}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      <div className="flex flex-col">
        <span className="truncate max-w-[200px] font-medium">
          {file.originalName || file.filename || "File"}
        </span>
        {file.size && (
          <span className="text-xs text-muted-foreground">
            {formatFileSize(file.size)}
          </span>
        )}
      </div>
      <Button
        type="button"
        size="sm"
        variant="ghost"
        className="h-6 w-6 p-0 ml-auto"
        onClick={onRemove}
      >
        <X className="h-3 w-3" />
        <span className="sr-only">Remove file</span>
      </Button>
    </div>
  );
}

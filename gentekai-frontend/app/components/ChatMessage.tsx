import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import SyntaxHighlighterModule from "react-syntax-highlighter/dist/cjs/prism";
import { vscDarkPlus } from "react-syntax-highlighter/dist/cjs/styles/prism";
import remarkGfm from "remark-gfm";
import { FilePreviewFetch } from "./FilePreviewFetch";

// SyntaxHighlighterModule is the component itself when importing from CJS.
const SyntaxHighlighter = SyntaxHighlighterModule;

export function ChatMessage({ content, sender, timestamp, extra }: Message) {
  const isUser = sender === "user";
  const time =
    timestamp ||
    new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  const hasFiles = extra?.file_urls && extra.file_urls.length > 0;
  return (
    <div
      className={cn(
        "flex items-start gap-3",
        isUser ? "justify-end" : "justify-start"
      )}
    >
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
          <span className="text-xs font-semibold">AI</span>
        </div>
      )}
      <div
        className={cn(
          "max-w-[80%] rounded-lg p-4",
          isUser
            ? "bg-primary text-primary-foreground rounded-tr-none"
            : "bg-secondary text-secondary-foreground rounded-tl-none"
        )}
      >
        {hasFiles && isUser && (
          <FilePreviewFetch fileUrls={extra.file_urls} className="mb-3" />
        )}
        {isUser ? (
          <div className="whitespace-pre-wrap break-words">{content}</div>
        ) : (
          <div className="markdown-content">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ node, ...props }) => (
                  <h1 className="text-2xl font-bold my-4" {...props} />
                ),
                h2: ({ node, ...props }) => (
                  <h2 className="text-xl font-bold my-3" {...props} />
                ),
                h3: ({ node, ...props }) => (
                  <h3 className="text-lg font-bold my-2" {...props} />
                ),
                h4: ({ node, ...props }) => (
                  <h4 className="text-base font-bold my-2" {...props} />
                ),
                p: ({ node, ...props }) => <p className="my-2" {...props} />,
                ul: ({ node, ...props }) => (
                  <ul className="list-disc pl-6 my-2" {...props} />
                ),
                ol: ({ node, ...props }) => (
                  <ol className="list-decimal pl-6 my-2" {...props} />
                ),
                li: ({ node, ...props }) => <li className="my-1" {...props} />,
                a: ({ node, ...props }) => (
                  <a
                    className="text-blue-500 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                    {...props}
                  />
                ),
                blockquote: ({ node, ...props }) => (
                  <blockquote
                    className="border-l-4 border-gray-300 pl-4 italic my-2"
                    {...props}
                  />
                ),
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || "");
                  return !inline && match ? (
                    <SyntaxHighlighter
                      style={vscDarkPlus}
                      language={match[1]}
                      PreTag="div"
                      className="rounded-md my-2"
                      {...props}
                    >
                      {String(children).replace(/\n$/, "")}
                    </SyntaxHighlighter>
                  ) : (
                    <code
                      className={cn(
                        "bg-gray-800 text-gray-100 rounded px-1 py-0.5",
                        inline ? "inline-block" : "block p-2 my-2",
                        className
                      )}
                      {...props}
                    >
                      {children}
                    </code>
                  );
                },
                table: ({ node, ...props }) => (
                  <div className="overflow-x-auto my-4 border rounded-md">
                    <table
                      className="min-w-full divide-y divide-gray-300 border-collapse"
                      {...props}
                    />
                  </div>
                ),
                thead: ({ node, ...props }) => (
                  <thead className="bg-gray-100" {...props} />
                ),
                tbody: ({ node, ...props }) => (
                  <tbody className="divide-y divide-gray-200" {...props} />
                ),
                tr: ({ node, ...props }) => (
                  <tr className="even:bg-gray-50" {...props} />
                ),
                th: ({ node, ...props }) => (
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    {...props}
                  />
                ),
                td: ({ node, ...props }) => (
                  <td className="px-4 py-3 text-sm" {...props} />
                ),
                hr: ({ node, ...props }) => (
                  <hr className="my-4 border-gray-300" {...props} />
                ),
                img: ({ node, ...props }) => (
                  <img
                    className="max-w-full h-auto rounded-md my-2"
                    {...props}
                    alt={props.alt || ""}
                  />
                ),
              }}
            >
              {content}
            </ReactMarkdown>
          </div>
        )}
        <div
          className={cn(
            "text-xs mt-1 opacity-70",
            isUser ? "text-right" : "text-left"
          )}
        >
          {time}
        </div>
      </div>
      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-secondary flex items-center justify-center text-secondary-foreground">
          <span className="text-xs font-semibold">You</span>
        </div>
      )}
    </div>
  );
}

export function ChatTypingIndicator() {
  return (
    <div className="flex items-start gap-3">
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
        <span className="text-xs font-semibold">AI</span>
      </div>
      <div className="bg-secondary text-secondary-foreground rounded-lg rounded-tl-none p-4 max-w-[80%]">
        <div className="flex space-x-2 items-center">
          <div
            className="h-2 w-2 bg-current rounded-full animate-bounce"
            style={{ animationDelay: "0ms" }}
          ></div>
          <div
            className="h-2 w-2 bg-current rounded-full animate-bounce"
            style={{ animationDelay: "150ms" }}
          ></div>
          <div
            className="h-2 w-2 bg-current rounded-full animate-bounce"
            style={{ animationDelay: "300ms" }}
          ></div>
        </div>
      </div>
    </div>
  );
}

"use client";

import { SignInButton } from "@clerk/remix";
import { LockIcon } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";

export default function AuthOverlay() {
  return (
    <div className="absolute inset-0 bg-blue-100/10 trans backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white/50 dark:bg-gray-900 p-8 rounded-lg shadow-lg max-w-md w-full">
        <div className="text-center mb-6">
          <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full inline-flex items-center justify-center mb-4">
            <LockIcon className="h-8 w-8 text-blue-600 dark:text-blue-300" />
          </div>
          <h2 className="text-2xl font-bold">Login Required</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Please log in to access
          </p>
        </div>

        <Button className="w-full" variant="outline">
          <SignInButton mode="modal">Sign In</SignInButton>
        </Button>

        <p className="text-center text-sm mt-4">
          This is a demo application.
        </p>
      </div>
    </div>
  );
}

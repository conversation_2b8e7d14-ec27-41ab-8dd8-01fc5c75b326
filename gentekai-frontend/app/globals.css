@tailwind base;
@tailwind components;
@tailwind utilities;

/*
  Theme variables
*/
@layer base {
  :root {
    /* Backgrounds */
    --background: 0 0% 98%;
    /* Softer white */
    --foreground: 222.2 84% 6%;
    /* Almost black, good contrast */

    --background-dark: 222.2 47.4% 7%;
    /* Deep blue-black */
    --foreground-dark: 210 40% 98%;
    /* White */

    /* Card */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 6%;
    --card-dark: 222.2 47.4% 12%;
    /* Slightly lighter than bg dark */
    --card-foreground-dark: 210 40% 98%;

    /* Popover */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 6%;
    --popover-dark: 222.2 47.4% 12%;
    --popover-foreground-dark: 210 40% 98%;

    /* Primary */
    --primary: 222 90% 44%;
    /* Vibrant blue */
    --primary-foreground: 210 40% 98%;
    --primary-dark: 222 100% 70%;
    /* Softer blue for dark mode */
    --primary-foreground-dark: 222.2 47.4% 12%;

    /* Secondary (sidebar bg, subtle gray/blue) */
    --secondary: 216 32% 94%;
    /* Light blue-gray */
    --secondary-foreground: 222.2 47.4% 12%;
    --secondary-dark: 216 32% 20%;
    /* Muted dark blue-gray */
    --secondary-foreground-dark: 210 40% 98%;

    /* Muted (chat bubbles, less contrast) */
    --muted: 210 16% 90%;
    /* Pale gray */
    --muted-foreground: 215.4 16.3% 46.9%;
    --muted-dark: 217.2 12% 24%;
    /* Duller blue-gray */
    --muted-foreground-dark: 215 20.2% 65.1%;

    /* Accent (active chat item, buttons) */
    --accent: 200 98% 65%;
    /* Vivid blue (light) */
    --accent-foreground: 222.2 47.4% 12%;
    --accent-dark: 200 98% 36%;
    /* Deep blue (dark) */
    --accent-foreground-dark: 210 40% 98%;

    /* Destructive (errors, danger) */
    --destructive: 0 84% 67%;
    --destructive-foreground: 210 40% 98%;
    --destructive-dark: 0 72% 38%;
    --destructive-foreground-dark: 210 40% 98%;

    /* Border & input */
    --border: 214.3 24% 85%;
    --input: 214.3 24% 85%;
    --ring: 222.2 84% 20%;
    --border-dark: 217.2 18% 24%;
    --input-dark: 217.2 18% 24%;
    --ring-dark: 212.7 26.8% 83.9%;

    --radius: 0.5rem;
  }
}

/*
  Theme switching based on Devon Govett's pattern
*/
@layer base {
  :root {
    --theme-light: initial;
    --theme-dark: ;
    color-scheme: light dark;
  }

  @media (prefers-color-scheme: dark) {
    :root {
      --theme-light: ;
      --theme-dark: initial;
    }
  }

  [data-theme="light"] {
    --theme-light: initial;
    --theme-dark: ;
    color-scheme: light;
  }

  [data-theme="dark"] {
    --theme-light: ;
    --theme-dark: initial;
    color-scheme: dark;

    /* Override for dark mode */
    --background: var(--background-dark);
    --foreground: var(--foreground-dark);
    --card: var(--card-dark);
    --card-foreground: var(--card-foreground-dark);
    --popover: var(--popover-dark);
    --popover-foreground: var(--popover-foreground-dark);
    --primary: var(--primary-dark);
    --primary-foreground: var(--primary-foreground-dark);
    --secondary: var(--secondary-dark);
    --secondary-foreground: var(--secondary-foreground-dark);
    --muted: var(--muted-dark);
    --muted-foreground: var(--muted-foreground-dark);
    --accent: var(--accent-dark);
    --accent-foreground: var(--accent-foreground-dark);
    --destructive: var(--destructive-dark);
    --destructive-foreground: var(--destructive-foreground-dark);
    --border: var(--border-dark);
    --input: var(--input-dark);
    --ring: var(--ring-dark);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced table styles for markdown content */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.markdown-content thead {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-content th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #4b5563;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.markdown-content td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-content tr:last-child td {
  border-bottom: none;
}

.markdown-content tr:nth-child(even) {
  background-color: #f9fafb;
}
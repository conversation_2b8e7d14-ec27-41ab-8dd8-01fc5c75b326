import { useAuth } from "@clerk/remix";

export function useAuthedFetch(): (
  input: RequestInfo | URL,
  init?: RequestInit
) => Promise<Response> {
  const { getToken } = useAuth();

  return async (
    input: RequestInfo | URL,
    init: RequestInit = {}
  ): Promise<Response> => {
    const token = await getToken();
    const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:8000/";
    return fetch(BACKEND_URL + input, {
      ...init,
      headers: {
        ...(init.headers || {}),
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  };
}

declare module "react-syntax-highlighter/dist/cjs/prism" {
	interface SyntaxHighlighterProps {
		language?: string;
		style?: any;
		children?: React.ReactNode;
		className?: string;
		PreTag?: React.ComponentType<any> | keyof JSX.IntrinsicElements;
		[key: string]: any;
	}

	const SyntaxHighlighter: React.ComponentType<SyntaxHighlighterProps>;
	export default SyntaxHighlighter;
}

declare module "react-syntax-highlighter/dist/cjs/styles/prism" {
	const vscDarkPlus: any;
	const dracula: any;
	const tomorrow: any;
	const vs: any;
	const xonokai: any;
	const atomDark: any;
	const prism: any;
	const okaidia: any;
	const solarizedlight: any;
	const materialDark: any;
	const materialLight: any;

	export {
		vscDarkPlus,
		dracula,
		tomorrow,
		vs,
		xonokai,
		atomDark,
		prism,
		okaidia,
		solarizedlight,
		materialDark,
		materialLight,
	};
}

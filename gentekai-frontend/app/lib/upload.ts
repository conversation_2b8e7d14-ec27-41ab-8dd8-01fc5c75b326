// Utility function to upload files to Cloudflare R2
export interface UploadedFile {
  url: string;
  filename: string;
  originalName: string;
  size: number;
  type: string;
  file_key: string;
}

export async function uploadToR2(file: File): Promise<UploadedFile> {
  const formData = new FormData();
  formData.append("file", file);

  try {
    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Upload failed");
    }

    const data = await response.json();
    console.log("File uploaded successfully:", { data });
    return {
      url: data.url,
      filename: file.name,
      originalName: data.originalName || file.name,
      size: data.size,
      type: data.type,
      file_key: data.file_key,
    };
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
}

// Utility function to validate file before upload
export function validateFile(file: File): { valid: boolean; error?: string } {
  const MAX_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "video/mp4",
    "video/webm",
    "audio/mp3",
    "audio/wav",
    "audio/ogg",
    "application/pdf",
    "text/plain",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  if (file.size > MAX_SIZE) {
    return {
      valid: false,
      error: `File size too large. Maximum size is ${MAX_SIZE / 1024 / 1024}MB`,
    };
  }

  if (!ALLOWED_TYPES.includes(file.type)) {
    return { valid: false, error: "File type not allowed" };
  }

  return { valid: true };
}

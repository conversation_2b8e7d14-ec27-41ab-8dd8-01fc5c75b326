import ChatInitiator from "@/components/ChatInitiator";
import { ConversationList } from "@/components/ConversationList";
import { Button } from "@/components/ui/button";
import { UploadedFile } from "@/lib/upload";
import { requireClerkAuth } from "@/services/auth.server";
import { LoaderFunctionArgs } from "@remix-run/node";
import { redirect, useFetcher, useLoaderData } from "@remix-run/react";
import { Menu } from "lucide-react";
import { useState } from "react";
export async function loader(arg: LoaderFunctionArgs) {
  try {
    const { token } = await requireClerkAuth(arg);
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL}/api/conversations`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      throw new Error(`Backend error: ${errorText}`);
    }
    const conversations = await backendResponse.json();
    if (!conversations) {
      return {
        conversations: [],
      };
    }
    console.log("Conversations loaded:", conversations);
    return conversations;
  } catch (error) {
    // handle error as needed
    console.error(
      "Error loading conversations:",
      (error as Response).headers.get("location")
    );
    if (error instanceof Response) {
      return redirect(error.headers.get("location") || "/sign-in");
    }

    return {
      conversations: [],
    };
  }
}

export async function action(arg: LoaderFunctionArgs) {
  try {
    const { token } = await requireClerkAuth(arg);
    const { request } = arg;
    const formData = await request.formData();
    const content = formData.get("content");
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL}/api/conversations/`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ title: content }),
      }
    );
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      throw new Error(`Backend error: ${errorText}`);
    }
    const conversation = await backendResponse.json();
    if (!conversation) {
      throw new Error("Failed to create conversation");
    }
    // redirect to the new conversation page
    const redirectUrl = `/chat/${conversation.id}`;
    return redirect(`${redirectUrl}?prompt=${content}`);
  } catch (error) {
    // handle error as needed
    console.error("Error creating conversation:", error);
    return new Response("Failed to create conversation", { status: 500 });
  }
}

export default function IndexLayout() {
  const { conversations: initialConversations } =
    useLoaderData<typeof loader>();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [conversations, setConversations] =
    useState<Conversation[]>(initialConversations);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const fetcher = useFetcher();

  const handleInitiateChat = (content: string, files?: UploadedFile[]) => {
    fetcher.submit(
      { content, files: files?.map((it) => it.file_key) ?? [] },
      { method: "post" }
    );
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Conversation List Component */}
      <ConversationList
        conversations={conversations}
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        isLoading={false}
      />

      {/* Main content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <header className="flex items-center p-4 border-b">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="mr-2 block md:hidden"
            aria-label="Toggle sidebar"
            type="button"
          >
            <Menu className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-semibold">GenTekAI</h2>
        </header>
        <ChatInitiator onSendMessage={handleInitiateChat} />
      </div>
    </div>
  );
}

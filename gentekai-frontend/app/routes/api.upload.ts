import { requireClerkAuth } from "@/services/auth.server";
import { LoaderFunctionArgs } from "@remix-run/node";

interface PresignedUrlResponse {
  presigned_url: string;
  file_key: string;
  upload_url: string;
}

interface UploadResult {
  success: boolean;
  file_key?: string;
  error?: string;
}
// Parse the multipart form data
export async function action(args: LoaderFunctionArgs) {
  try {
    const { request } = args;
    const { token } = await requireClerkAuth(args);
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      throw new Response("No file provided", { status: 400 });
    }

    // Validate file size (e.g., 10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Response("File too large", { status: 400 });
    }

    // Step 1: Get presigned URL from FastAPI backend
    const presignedResponse = await fetch(
      `${process.env.BACKEND_URL}/api/upload/presigned-url`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        }),
      }
    );

    if (!presignedResponse.ok) {
      const errorText = await presignedResponse.text();
      throw new Response(`Failed to get presigned URL: ${errorText}`, {
        status: 500,
      });
    }

    const presignedData: PresignedUrlResponse = await presignedResponse.json();

    console.info("presignedData", { presignedData });

    // Step 2: Upload file to Cloudflare R2 using presigned URL
    const uploadResponse = await fetch(presignedData.presigned_url, {
      method: "PUT",
      headers: {
        "Content-Type": file.type,
      },
      body: file,
    });

    if (!uploadResponse.ok) {
      console.error("Upload failed:", {
        status: uploadResponse.status,
        statusText: uploadResponse.statusText,
        url: presignedData.presigned_url,
      });
      const errorText = await uploadResponse.text();
      console.error("Upload error:", errorText);

      throw new Response("Failed to upload file to R2", { status: 500 });
    }

    // // Step 3: Optionally notify your backend that upload completed
    // await fetch(`${process.env.FASTAPI_BASE_URL}/upload/complete`, {
    //   method: "POST",
    //   headers: {
    //     "Content-Type": "application/json",
    //     Authorization: `Bearer ${process.env.API_TOKEN}`,
    //   },
    //   body: JSON.stringify({
    //     file_key: presignedData.file_key,
    //     filename: file.name,
    //     file_size: file.size,
    //     content_type: file.type,
    //   }),
    // });

    // Return plain object - Remix will serialize it
    return {
      success: true,
      file_key: presignedData.file_key,
      url: presignedData.upload_url,
    };
  } catch (error) {
    if (error instanceof Response) {
      throw error; // Re-throw Response errors
    }

    console.error("Upload error:", error);
    throw new Response("Internal server error", { status: 500 });
  }
}

import { ChatInput, type ChatInputRef } from "@/components/ChatInput";
import { ChatMessageList } from "@/components/ChatMessageList";
import { UploadedFile } from "@/lib/upload";
import { requireClerkAuth } from "@/services/auth.server";
import { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useParams, useSearchParams } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";

// --- <PERSON><PERSON> fetches all previous messages SSR ---
export async function loader(arg: LoaderFunctionArgs) {
  const conversationId = arg.params.id!;
  try {
    const { token } = await requireClerkAuth(arg);
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL}/api/conversations/${conversationId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      throw new Error(`Backend error: ${errorText}`);
    }
    const messages = await backendResponse.json();
    if (!messages) {
      return {
        messages: [],
      };
    }
    return messages;
  } catch (error) {
    // handle error as needed
    return {
      conversations: [],
    };
  }
}

// --- Main Chat Page Component ---
export default function ChatPage() {
  const { messages: initialMessages } = useLoaderData<typeof loader>();
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const conversationId = params.id!;
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedResponse, setStreamedResponse] = useState("");
  const chatInputRef = useRef<ChatInputRef>(null);
  // --- Send message via streaming endpoint ---
  const handleSendMessage = async (content: string, files?: UploadedFile[]) => {
    if (!content.trim() || isStreaming) return;

    // Show user's message immediately
    const userMessage: Message = {
      id: `temp-${Date.now()}`,
      sender: "user",
      content,
      timestamp: new Date().toISOString(),
      conversationId,
      extra: {
        file_keys: files?.map((it) => it.filename) ?? [],
        file_urls:
          files?.map((it) => {
            return {
              file_key: it.file_key,
              url: it.url,
              filename: it.filename,
            };
          }) ?? [],
      },
    };
    setMessages((prev) => [...prev, userMessage]);
    setIsStreaming(true);
    setStreamedResponse("");

    // Temp assistant message (to be updated as streaming progresses)
    const tempAssistantMessage: Message = {
      id: `temp-response-${Date.now()}`,
      sender: "assistant",
      content: "",
      timestamp: new Date().toISOString(),
      conversationId,
    };
    setMessages((prev) => [...prev, tempAssistantMessage]);

    try {
      // POST to resource route that supports streaming (see below)
      const res = await fetch(`/api/chat/${conversationId}/send`, {
        method: "POST",
        body: JSON.stringify({
          content,
          file_keys: files?.map((it) => it.file_key) ?? [],
        }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.body) throw new Error("No response body for streaming");

      const reader = res.body.getReader();
      let fullText = "";
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = new TextDecoder().decode(value);
        fullText += chunk;
        setStreamedResponse(fullText);

        // Update temp assistant message in messages list
        setMessages((prev) => {
          const updated = [...prev];
          const lastIndex = updated.length - 1;
          if (
            lastIndex >= 0 &&
            updated[lastIndex].id?.startsWith("temp-response-")
          ) {
            updated[lastIndex] = {
              ...updated[lastIndex],
              content: fullText,
            };
          }
          return updated;
        });
      }
    } catch (error) {
      setMessages((prev) => {
        const updated = [...prev];
        const lastIndex = updated.length - 1;
        if (
          lastIndex >= 0 &&
          updated[lastIndex].id?.startsWith("temp-response-")
        ) {
          updated[lastIndex] = {
            ...updated[lastIndex],
            content:
              "Sorry, there was an error processing your request. Please try again.",
          };
        }
        return updated;
      });
    } finally {
      setIsStreaming(false);
      if (searchParams.get("prompt")) {
        setSearchParams({}); // Clear prompt from URL
      }
      // Remove temp assistant message
    }
  };

  const hasPromptSentRef = useRef(false);

  useEffect(() => {
    if (
      searchParams.get("prompt") &&
      !isStreaming &&
      messages.length === 0 &&
      !messages[0]?.content &&
      !hasPromptSentRef.current
    ) {
      const prompt = searchParams.get("prompt");
      if (prompt) {
        handleSendMessage(prompt);
        hasPromptSentRef.current = true; // Prevent double send
      }
    }
    return () => {};
    // Only want this to run once per conversation
  }, [conversationId]);
  // For focus UX
  useEffect(() => {
    if (!isStreaming) {
      setTimeout(() => chatInputRef.current?.focus(), 100);
    }
  }, [isStreaming]);
  // Whenever conversation changes, reset messages state to loader's result
  useEffect(() => {
    if (searchParams.get("prompt")) {
      return;
    }
    setMessages(initialMessages);
  }, [initialMessages, conversationId]);

  return (
    <>
      <ChatMessageList
        messages={messages}
        isLoading={false}
        isStreaming={isStreaming}
        streamedText={streamedResponse}
      />
      <ChatInput
        ref={chatInputRef}
        isSubmitting={isStreaming}
        onSendMessage={handleSendMessage}
        disabled={isStreaming}
      />
    </>
  );
}

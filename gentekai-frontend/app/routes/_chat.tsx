"use client";

import { ConversationList } from "@/components/ConversationList";
import { Button } from "@/components/ui/button";
import { requireClerkAuth } from "@/services/auth.server";
import { LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, useLoaderData } from "@remix-run/react";
import { Menu } from "lucide-react";
import { useState } from "react";

export async function loader(arg: LoaderFunctionArgs) {
  try {
    const { token } = await requireClerkAuth(arg);
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL}/api/conversations`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      throw new Error(`Backend error: ${errorText}`);
    }
    const conversations = await backendResponse.json();
    if (!conversations) {
      return {
        conversations: [],
      };
    }
    return conversations;
  } catch (error) {
    // handle error as needed
    return {
      conversations: [],
    };
  }
}

export default function ChatLayout() {
  const { conversations: initialConversations } =
    useLoaderData<typeof loader>();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [conversations, setConversations] =
    useState<Conversation[]>(initialConversations);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Conversation List Component */}
      <ConversationList
        conversations={conversations}
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        isLoading={false}
      />

      {/* Main content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <header className="flex items-center p-4 border-b">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="mr-2 block md:hidden"
            aria-label="Toggle sidebar"
            data-testid="toggle-sidebar-button"
            type="button"
          >
            <Menu className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-semibold">GenTekAI</h2>
        </header>

        <Outlet />
      </div>
    </div>
  );
}

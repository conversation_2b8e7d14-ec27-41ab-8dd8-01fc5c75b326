// app/routes/api/chat.$id.send.ts
import { requireClerkAuth } from "@/services/auth.server";
import type { LoaderFunctionArgs } from "@remix-run/node";

export async function action(args: LoaderFunctionArgs) {
  const { request, params } = args;

  const { token } = await requireClerkAuth(args);

  const conversationId = params.id;
  if (!conversationId) {
    return new Response("Missing conversation ID", { status: 400 });
  }

  // Parse message content from POST body (assume JSON)
  const { content, file_keys } = await request.json();
  if (!content || typeof content !== "string") {
    return new Response("Missing message content", { status: 400 });
  }

  // Call your backend API (which returns a streaming response)
  const backendResponse = await fetch(
    `${process.env.BACKEND_URL}/api/chat/stream`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        prompt: content,
        conversation_id: conversationId,
        file_keys: file_keys || [], // Optional array of file keys
      }),
    }
  );

  if (!backendResponse.ok) {
    const errorText = await backendResponse.text();
    return new Response(`Backend error: ${errorText}`, {
      status: backendResponse.status,
    });
  }

  // ✅ Forward the backend streaming response directly
  return new Response(backendResponse.body, {
    status: backendResponse.status,
    headers: {
      // Use the backend's content-type, or set your own
      "Content-Type":
        backendResponse.headers.get("Content-Type") ?? "text/plain",
      // You may want to copy other headers as well
      // For CORS, custom headers, etc.
    },
  });
}

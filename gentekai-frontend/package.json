{"name": "my-remix-app", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "dotenv -- node ./server.js", "start": "cross-env NODE_ENV=production node ./server.js", "typecheck": "tsc"}, "dependencies": {"@clerk/remix": "^4.7.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-slot": "1.0.2", "@remix-run/express": "^2.16.7", "@remix-run/node": "^2.16.7", "@remix-run/react": "^2.16.7", "@remix-run/serve": "^2.16.7", "class-variance-authority": "0.7.0", "clsx": "2.1.0", "compression": "1.7.4", "cross-env": "7.0.3", "express": "4.18.2", "isbot": "5.1.0", "lucide-react": "^0.511.0", "morgan": "1.10.0", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "remix-utils": "7.5.0", "tailwind-merge": "2.2.1", "tailwindcss-animate": "1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@remix-run/dev": "^2.16.7", "@tailwindcss/typography": "0.5.10", "@types/node": "20.11.19", "@types/react": "18.2.56", "@types/react-dom": "18.2.19", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "10.4.17", "dotenv-cli": "7.3.0", "eslint": "^9.27.0", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "5.3.3", "vite": "5.1.3", "vite-env-only": "2.2.0", "vite-tsconfig-paths": "4.3.1"}, "engines": {"node": ">=18.0.0"}}
{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll": "explicit", "source.organizeImports": "explicit", "source.fixAll.stylelint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnPaste": true, "eslint.validate": ["typescript", "typescriptreact", "javascript", "javascriptreact"]}
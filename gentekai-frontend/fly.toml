app = "remix-shadcn"
kill_signal = "SIGINT"
kill_timeout = 5
processes = []

[env]
PORT = "3000"

[mounts]
source = "remix_shadcn_data"
destination = "/data"

[[services]]
internal_port = 3000
processes = ["app"]
protocol = "tcp"
script_checks = []

[services.concurrency]
hard_limit = 50
soft_limit = 40
type = "connections"

[[services.ports]]
force_https = true
handlers = ["http"]
port = 80

[[services.ports]]
handlers = ["tls", "http"]
port = 443

[[services.tcp_checks]]
grace_period = "60s"
interval = "15s"
restart_limit = 6
timeout = "2s"

[[services.http_checks]]
grace_period = "5s"
headers = {}
interval = 10_000
method = "get"
path = "/healthcheck"
protocol = "http"
timeout = 2_000
tls_skip_verify = false

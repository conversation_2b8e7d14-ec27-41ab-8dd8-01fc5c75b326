generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  binaryTargets   = ["native", "linux-musl"]
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

model agent_tool_logs {
  id        Int       @id @default(autoincrement())
  user_id   String    @db.Uuid
  tool_name String?   @db.VarChar(100)
  params    Json?
  result    Json?
  success   Boolean?
  timestamp DateTime? @db.Timestamp(6)
  users     users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model applications {
  id                                           Int                @id @default(autoincrement())
  candidate_id                                 String             @db.Uuid
  position_id                                  Int
  applied_at                                   DateTime?          @db.Timestamp(6)
  status                                       applicationstatus?
  notes                                        String?
  created_at                                   DateTime?          @db.Timestamp(6)
  updated_at                                   DateTime?          @db.Timestamp(6)
  last_updated_by_id                           String?            @db.Uuid
  users_applications_candidate_idTousers       users              @relation("applications_candidate_idTousers", fields: [candidate_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_applications_last_updated_by_idTousers users?             @relation("applications_last_updated_by_idTousers", fields: [last_updated_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  positions                                    positions          @relation(fields: [position_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model conversations {
  id              String     @id @default(uuid()) @db.Uuid
  user_id         String     @db.Uuid
  slack_thread_id String?    @db.VarChar(100)
  title           String     @db.VarChar(255)
  created_at      DateTime   @db.Timestamp(6)
  updated_at      DateTime   @db.Timestamp(6)
  is_deleted      Boolean
  deleted_at      DateTime?  @db.Timestamp(6)
  is_archived     Boolean
  archived_at     DateTime?  @db.Timestamp(6)
  message_count   Int
  last_message_at DateTime?  @db.Timestamp(6)
  users           users      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  messages        messages[]

  @@index([is_archived], map: "idx_conversations_archived")
  @@index([is_deleted], map: "idx_conversations_deleted")
  @@index([last_message_at], map: "idx_conversations_last_message")
  @@index([title], map: "idx_conversations_title_search")
  @@index([user_id, is_deleted, is_archived], map: "idx_conversations_user_active")
  @@index([user_id, created_at], map: "idx_conversations_user_created")
  @@index([user_id, updated_at], map: "idx_conversations_user_updated")
}

model cv_data {
  id         Int                    @id @default(autoincrement())
  user_id    String                 @db.Uuid
  name       String                 @db.VarChar(100)
  data       Json
  created_at DateTime?              @db.Timestamp(6)
  updated_at DateTime?              @db.Timestamp(6)
  embedding  Unsupported("vector")?
  is_public  Boolean?
  source     String?                @db.VarChar(50)
  users      users                  @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model cv_structure_templates {
  id          Int       @id @default(autoincrement())
  name        String    @db.VarChar(100)
  description String?   @db.VarChar
  template    Json      @db.Json
  created_at  DateTime? @db.Timestamp(6)
  updated_at  DateTime? @db.Timestamp(6)
  is_active   Boolean?  @default(true)
  is_default  Boolean?  @default(false)
}

model departments {
  id          Int         @id @default(autoincrement())
  name        String      @db.VarChar(100)
  description String?
  manager_id  String?     @db.Uuid
  created_at  DateTime?   @db.Timestamp(6)
  updated_at  DateTime?   @db.Timestamp(6)
  users       users?      @relation(fields: [manager_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  positions   positions[]
}

model messages {
  id               String                 @id @default(uuid()) @db.Uuid
  conversation_id  String                 @db.Uuid
  sender           String                 @db.VarChar(20)
  content          String
  embedding        Unsupported("vector")?
  created_at       DateTime               @db.Timestamp(6)
  updated_at       DateTime               @db.Timestamp(6)
  agent_used       String?                @db.VarChar(50)
  response_time_ms Int?
  tokens_used      Int?
  is_edited        Boolean
  edit_count       Int
  character_count  Int?
  is_deleted       Boolean
  deleted_at       DateTime?              @db.Timestamp(6)
  extra            Json?
  conversations    conversations          @relation(fields: [conversation_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([agent_used, response_time_ms], map: "idx_messages_agent_response_time")
  @@index([content], map: "idx_messages_content_search")
  @@index([conversation_id, created_at], map: "idx_messages_conversation_created")
  @@index([conversation_id, sender], map: "idx_messages_conversation_sender")
  @@index([created_at], map: "idx_messages_created_at")
  @@index([is_deleted], map: "idx_messages_deleted")
  @@index([sender, created_at], map: "idx_messages_sender_created")
}

model positions {
  id                                       Int                    @id @default(autoincrement())
  title                                    String                 @db.VarChar(100)
  department_id                            Int?
  hiring_manager_id                        String?                @db.Uuid
  job_description                          String
  requirements                             String?
  nice_to_have                             String?
  responsibilities                         String?
  employment_type                          employmenttype?
  experience_level                         experiencelevel?
  salary_min                               Decimal?               @db.Decimal(10, 2)
  salary_max                               Decimal?               @db.Decimal(10, 2)
  location                                 String?                @db.VarChar(100)
  remote_allowed                           Boolean?
  status                                   positionstatus?
  posted_date                              DateTime?              @db.Timestamp(6)
  closing_date                             DateTime?              @db.Timestamp(6)
  positions_available                      Int?
  created_at                               DateTime?              @db.Timestamp(6)
  updated_at                               DateTime?              @db.Timestamp(6)
  created_by_id                            String                 @db.Uuid
  currency                                 currency?              @default(EUR)
  embedding                                Unsupported("vector")?
  applications                             applications[]
  users_positions_created_by_idTousers     users                  @relation("positions_created_by_idTousers", fields: [created_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  departments                              departments?           @relation(fields: [department_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_positions_hiring_manager_idTousers users?                 @relation("positions_hiring_manager_idTousers", fields: [hiring_manager_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model skills {
  id          Int           @id @default(autoincrement())
  name        String        @unique @db.VarChar(100)
  user_skills user_skills[]
}

model user_skills {
  id          Int    @id @default(autoincrement())
  user_id     String @db.Uuid
  skill_id    Int
  proficiency Int?
  skills      skills @relation(fields: [skill_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users       users  @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  id                                                  String             @id @default(uuid()) @db.Uuid
  clerk_user_id                                       String?            @unique @db.VarChar(64)
  slack_user_id                                       String?            @unique @db.VarChar(64)
  email                                               String             @unique @db.VarChar(255)
  first_name                                          String?            @db.VarChar(100)
  last_name                                           String?            @db.VarChar(100)
  role                                                user_role?
  created_at                                          DateTime?          @db.Timestamp(6)
  updated_at                                          DateTime?          @db.Timestamp(6)
  profile                                             Json?
  is_active                                           Boolean?           @default(true)
  agent_tool_logs                                     agent_tool_logs[]
  applications_applications_candidate_idTousers       applications[]     @relation("applications_candidate_idTousers")
  applications_applications_last_updated_by_idTousers applications[]     @relation("applications_last_updated_by_idTousers")
  conversations                                       conversations[]
  cv_data                                             cv_data[]
  departments                                         departments[]
  positions_positions_created_by_idTousers            positions[]        @relation("positions_created_by_idTousers")
  positions_positions_hiring_manager_idTousers        positions[]        @relation("positions_hiring_manager_idTousers")
  user_skills                                         user_skills[]
  workflow_records                                    workflow_records[]
}

model workflow_records {
  id          Int       @id @default(autoincrement())
  type        String?   @db.VarChar(50)
  status      String?   @db.VarChar(20)
  user_id     String    @db.Uuid
  input_data  Json?
  result_data Json?
  created_at  DateTime? @db.Timestamp(6)
  updated_at  DateTime? @db.Timestamp(6)
  users       users     @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

enum applicationstatus {
  APPLIED
  REVIEWING
  INTERVIEW_SCHEDULED
  INTERVIEW_COMPLETED
  OFFER_EXTENDED
  HIRED
  REJECTED
  WITHDRAWN
}

enum employmenttype {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERN
  FREELANCE
}

enum experiencelevel {
  ENTRY
  MID
  SENIOR
  LEAD
  EXECUTIVE
}

enum positionstatus {
  DRAFT
  OPEN
  ON_HOLD
  FILLED
  CANCELLED
}

enum user_role {
  ADMIN
  USER
  OWNER
  EXTERNAL
}

enum currency {
  USD
  EUR
  VND
}

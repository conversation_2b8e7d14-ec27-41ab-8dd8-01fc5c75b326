-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "vector";

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "applicationstatus" AS ENUM ('APPLIED', 'REVIEWING', 'INTERVIEW_SCHEDULED', 'INTERVIEW_COMPLETED', 'OFFER_EXTENDED', 'HIRED', 'REJECTED', 'WITHDRAWN');

-- <PERSON>reateEnum
CREATE TYPE "employmenttype" AS ENUM ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN', 'FREELANCE');

-- CreateEnum
CREATE TYPE "experiencelevel" AS ENUM ('ENTRY', 'MID', 'SENIOR', 'LEAD', 'EXECUTIVE');

-- CreateEnum
CREATE TYPE "positionstatus" AS ENUM ('DRAFT', 'OPEN', 'ON_HOLD', 'FILLED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "user_role" AS ENUM ('ADMIN', 'USER', 'OWNER', 'EXTERNAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "currency" AS ENUM ('USD', 'EUR', 'VND');

-- CreateTable
CREATE TABLE "agent_tool_logs" (
    "id" SERIAL NOT NULL,
    "user_id" UUID NOT NULL,
    "tool_name" VARCHAR(100),
    "params" JSONB,
    "result" JSONB,
    "success" BOOLEAN,
    "timestamp" TIMESTAMP(6),

    CONSTRAINT "agent_tool_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "applications" (
    "id" SERIAL NOT NULL,
    "candidate_id" UUID NOT NULL,
    "position_id" INTEGER NOT NULL,
    "applied_at" TIMESTAMP(6),
    "status" "applicationstatus",
    "notes" TEXT,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),
    "last_updated_by_id" UUID,

    CONSTRAINT "applications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "conversations" (
    "id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL,
    "updated_at" TIMESTAMP(6) NOT NULL,
    "is_deleted" BOOLEAN NOT NULL,
    "deleted_at" TIMESTAMP(6),
    "is_archived" BOOLEAN NOT NULL,
    "archived_at" TIMESTAMP(6),
    "message_count" INTEGER NOT NULL,
    "last_message_at" TIMESTAMP(6),

    CONSTRAINT "conversations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cv_data" (
    "id" SERIAL NOT NULL,
    "user_id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "data" JSONB NOT NULL,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),
    "embedding" vector,
    "is_public" BOOLEAN,
    "source" VARCHAR(50),

    CONSTRAINT "cv_data_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cv_structure_templates" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR,
    "template" JSON NOT NULL,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),
    "is_active" BOOLEAN DEFAULT true,
    "is_default" BOOLEAN DEFAULT false,

    CONSTRAINT "cv_structure_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "departments" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "manager_id" UUID,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "departments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "messages" (
    "id" UUID NOT NULL,
    "conversation_id" UUID NOT NULL,
    "sender" VARCHAR(20) NOT NULL,
    "content" TEXT NOT NULL,
    "embedding" vector,
    "created_at" TIMESTAMP(6) NOT NULL,
    "updated_at" TIMESTAMP(6) NOT NULL,
    "agent_used" VARCHAR(50),
    "response_time_ms" INTEGER,
    "tokens_used" INTEGER,
    "is_edited" BOOLEAN NOT NULL,
    "edit_count" INTEGER NOT NULL,
    "character_count" INTEGER,
    "is_deleted" BOOLEAN NOT NULL,
    "deleted_at" TIMESTAMP(6),
    "extra" JSONB,

    CONSTRAINT "messages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "positions" (
    "id" SERIAL NOT NULL,
    "title" VARCHAR(100) NOT NULL,
    "department_id" INTEGER,
    "hiring_manager_id" UUID,
    "job_description" TEXT NOT NULL,
    "requirements" TEXT,
    "nice_to_have" TEXT,
    "responsibilities" TEXT,
    "employment_type" "employmenttype",
    "experience_level" "experiencelevel",
    "salary_min" DECIMAL(10,2),
    "salary_max" DECIMAL(10,2),
    "location" VARCHAR(100),
    "remote_allowed" BOOLEAN,
    "status" "positionstatus",
    "posted_date" TIMESTAMP(6),
    "closing_date" TIMESTAMP(6),
    "positions_available" INTEGER,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),
    "created_by_id" UUID NOT NULL,
    "currency" "currency" DEFAULT 'EUR',
    "embedding" vector,

    CONSTRAINT "positions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "skills" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,

    CONSTRAINT "skills_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_skills" (
    "id" SERIAL NOT NULL,
    "user_id" UUID NOT NULL,
    "skill_id" INTEGER NOT NULL,
    "proficiency" INTEGER,

    CONSTRAINT "user_skills_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" UUID NOT NULL,
    "clerk_user_id" VARCHAR(64),
    "email" VARCHAR(255) NOT NULL,
    "first_name" VARCHAR(100),
    "last_name" VARCHAR(100),
    "role" "user_role",
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),
    "profile" JSONB,
    "is_active" BOOLEAN DEFAULT true,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow_records" (
    "id" SERIAL NOT NULL,
    "type" VARCHAR(50),
    "status" VARCHAR(20),
    "user_id" UUID NOT NULL,
    "input_data" JSONB,
    "result_data" JSONB,
    "created_at" TIMESTAMP(6),
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "workflow_records_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_conversations_archived" ON "conversations"("is_archived");

-- CreateIndex
CREATE INDEX "idx_conversations_deleted" ON "conversations"("is_deleted");

-- CreateIndex
CREATE INDEX "idx_conversations_last_message" ON "conversations"("last_message_at");

-- CreateIndex
CREATE INDEX "idx_conversations_title_search" ON "conversations"("title");

-- CreateIndex
CREATE INDEX "idx_conversations_user_active" ON "conversations"("user_id", "is_deleted", "is_archived");

-- CreateIndex
CREATE INDEX "idx_conversations_user_created" ON "conversations"("user_id", "created_at");

-- CreateIndex
CREATE INDEX "idx_conversations_user_updated" ON "conversations"("user_id", "updated_at");

-- CreateIndex
CREATE INDEX "idx_messages_agent_response_time" ON "messages"("agent_used", "response_time_ms");

-- CreateIndex
CREATE INDEX "idx_messages_content_search" ON "messages"("content");

-- CreateIndex
CREATE INDEX "idx_messages_conversation_created" ON "messages"("conversation_id", "created_at");

-- CreateIndex
CREATE INDEX "idx_messages_conversation_sender" ON "messages"("conversation_id", "sender");

-- CreateIndex
CREATE INDEX "idx_messages_created_at" ON "messages"("created_at");

-- CreateIndex
CREATE INDEX "idx_messages_deleted" ON "messages"("is_deleted");

-- CreateIndex
CREATE INDEX "idx_messages_sender_created" ON "messages"("sender", "created_at");

-- CreateIndex
CREATE UNIQUE INDEX "skills_name_key" ON "skills"("name");

-- CreateIndex
CREATE UNIQUE INDEX "users_clerk_user_id_key" ON "users"("clerk_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- AddForeignKey
ALTER TABLE "agent_tool_logs" ADD CONSTRAINT "agent_tool_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_candidate_id_fkey" FOREIGN KEY ("candidate_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_last_updated_by_id_fkey" FOREIGN KEY ("last_updated_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "cv_data" ADD CONSTRAINT "cv_data_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "departments" ADD CONSTRAINT "departments_manager_id_fkey" FOREIGN KEY ("manager_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "positions" ADD CONSTRAINT "positions_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "positions" ADD CONSTRAINT "positions_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "positions" ADD CONSTRAINT "positions_hiring_manager_id_fkey" FOREIGN KEY ("hiring_manager_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_skills" ADD CONSTRAINT "user_skills_skill_id_fkey" FOREIGN KEY ("skill_id") REFERENCES "skills"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_skills" ADD CONSTRAINT "user_skills_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "workflow_records" ADD CONSTRAINT "workflow_records_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

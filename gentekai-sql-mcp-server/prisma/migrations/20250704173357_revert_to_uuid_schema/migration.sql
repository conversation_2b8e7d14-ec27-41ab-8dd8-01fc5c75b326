/*
  Revert UUID schema: Convert TEXT back to UUID for id columns
*/

-- Drop the unique constraints that were added in the previous migration
DROP INDEX IF EXISTS "conversations_id_key";
DROP INDEX IF EXISTS "messages_id_key";

-- Drop foreign key constraint first to avoid issues during type conversion
ALTER TABLE "messages" DROP CONSTRAINT IF EXISTS "messages_conversation_id_fkey";

-- Convert conversations.id from TEXT to UUID
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_pkey";
ALTER TABLE "conversations" ALTER COLUMN "id" SET DATA TYPE UUID USING id::uuid;
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");

-- Convert messages.id from TEXT to UUID
ALTER TABLE "messages" DROP CONSTRAINT "messages_pkey";
ALTER TABLE "messages" ALTER COLUMN "id" SET DATA TYPE UUID USING id::uuid;
ALTER TABLE "messages" ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");

-- Update foreign key reference to use UUID
ALTER TABLE "messages" ALTER COLUMN "conversation_id" SET DATA TYPE UUID USING conversation_id::uuid;

-- Recreate foreign key constraint
ALTER TABLE "messages"
ADD CONSTRAINT "messages_conversation_id_fkey"
FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

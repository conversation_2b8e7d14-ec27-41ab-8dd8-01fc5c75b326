{"version": "0.2.0", "configurations": [{"name": "Run in Development Mode", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal"}, {"name": "Run in Production Mode", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start"], "console": "integratedTerminal", "preLaunchTask": "npm: build"}]}
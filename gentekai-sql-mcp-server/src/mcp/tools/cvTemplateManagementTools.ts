import { CvStructureService } from "@/services/cv/cvStructureService";
import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { z } from "zod";

// Initialize CV structure service
const cvStructureService = new CvStructureService();

// Schema definitions for validation
const CreateTemplateSchema = z.object({
	name: z.string().min(1).max(100),
	description: z.string().optional(),
	template: z.record(z.any()),
	is_active: z.boolean().optional().default(true),
	is_default: z.boolean().optional().default(false),
});

const UpdateTemplateSchema = z.object({
	id: z.number().int().positive(),
	name: z.string().min(1).max(100).optional(),
	description: z.string().optional(),
	template: z.record(z.any()).optional(),
	is_active: z.boolean().optional(),
	is_default: z.boolean().optional(),
});

const GetTemplateSchema = z.object({
	id: z.number().int().positive(),
});

const GetTemplateByNameSchema = z.object({
	name: z.string().min(1),
});

const ListTemplatesSchema = z.object({
	is_active: z.boolean().optional(),
	is_default: z.boolean().optional(),
	search: z.string().optional(),
	limit: z.number().min(1).max(100).optional().default(50),
	offset: z.number().min(0).optional().default(0),
});

const SetDefaultTemplateSchema = z.object({
	id: z.number().int().positive(),
});

const DuplicateTemplateSchema = z.object({
	id: z.number().int().positive(),
	new_name: z.string().min(1).max(100).optional(),
});

const UpdateTemplateDataSchema = z.object({
	id: z.number().int().positive(),
	template: z.record(z.any()),
});

const ToggleTemplateStatusSchema = z.object({
	id: z.number().int().positive(),
});

const GetTemplatesByStatusSchema = z.object({
	is_active: z.boolean(),
});

export const addCvTemplateManagementTools = (server: McpServer): McpServer => {
	// Create CV Template Tool
	server.tool(
		"create_cv_template",
		"Create a new CV structure template with name, description, and template structure",
		{
			name: z.string().min(1).max(100).describe("Template name (must be unique)"),
			description: z.string().optional().describe("Optional description of the template"),
			template: z.record(z.any()).describe("Template structure as JSON object defining CV sections and fields"),
			is_active: z.boolean().optional().default(true).describe("Whether template is active"),
			is_default: z.boolean().optional().default(false).describe("Whether this should be the default template"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: create_cv_template called with args:", args);

				const validatedArgs = CreateTemplateSchema.parse(args);
				const template = await cvStructureService.createTemplate(validatedArgs);

				const result = {
					success: true,
					message: "CV template created successfully",
					template,
				};

				console.log("MCP Tool: create_cv_template result:", `Created template with ID: ${template.id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: create_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get CV Template Tool
	server.tool(
		"get_cv_template",
		"Retrieve detailed CV template information by ID",
		{
			id: z.number().int().positive().describe("Template ID to fetch"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_cv_template called with args:", args);

				const validatedArgs = GetTemplateSchema.parse(args);
				const template = await cvStructureService.getTemplateById(validatedArgs.id);

				if (!template) {
					const result = {
						success: false,
						message: "Template not found",
					};

					console.log("MCP Tool: get_cv_template result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					template,
				};

				console.log("MCP Tool: get_cv_template result:", `Found template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get CV Template by Name Tool
	server.tool(
		"get_cv_template_by_name",
		"Retrieve CV template information by name",
		{
			name: z.string().min(1).describe("Template name to search for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_cv_template_by_name called with args:", args);

				const validatedArgs = GetTemplateByNameSchema.parse(args);
				const template = await cvStructureService.getTemplateByName(validatedArgs.name);

				if (!template) {
					const result = {
						success: false,
						message: "Template not found",
					};

					console.log("MCP Tool: get_cv_template_by_name result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					template,
				};

				console.log("MCP Tool: get_cv_template_by_name result:", `Found template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_cv_template_by_name error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update CV Template Tool
	server.tool(
		"update_cv_template",
		"Update existing CV template information such as name, description, structure, or status",
		{
			id: z.number().int().positive().describe("Template ID to update"),
			name: z.string().min(1).max(100).optional().describe("Updated template name"),
			description: z.string().optional().describe("Updated description"),
			template: z.record(z.any()).optional().describe("Updated template structure (will be merged with existing)"),
			is_active: z.boolean().optional().describe("Updated active status"),
			is_default: z.boolean().optional().describe("Updated default status"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_cv_template called with args:", args);

				const validatedArgs = UpdateTemplateSchema.parse(args);
				const { id, ...updates } = validatedArgs;
				const template = await cvStructureService.updateTemplate(id, updates);

				const result = {
					success: true,
					message: "CV template updated successfully",
					template,
				};

				console.log("MCP Tool: update_cv_template result:", `Updated template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// List CV Templates Tool
	server.tool(
		"list_cv_templates",
		"List CV templates with optional filtering by status, default setting, or search criteria",
		{
			is_active: z.boolean().optional().describe("Filter by active status"),
			is_default: z.boolean().optional().describe("Filter by default status"),
			search: z.string().optional().describe("Search in template names and descriptions"),
			limit: z.number().min(1).max(100).optional().default(50).describe("Limit number of results (1-100)"),
			offset: z.number().min(0).optional().default(0).describe("Offset for pagination"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: list_cv_templates called with args:", args);

				const validatedArgs = ListTemplatesSchema.parse(args);
				const { templates, total } = await cvStructureService.listTemplates(validatedArgs);

				const result = {
					success: true,
					total,
					count: templates.length,
					templates,
					pagination: {
						limit: validatedArgs.limit,
						offset: validatedArgs.offset,
						hasMore: validatedArgs.offset + templates.length < total,
					},
				};

				console.log("MCP Tool: list_cv_templates result:", `Found ${templates.length} templates out of ${total} total`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: list_cv_templates error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Delete CV Template Tool
	server.tool(
		"delete_cv_template",
		"Delete a CV template (soft delete - sets is_active to false)",
		{
			id: z.number().int().positive().describe("Template ID to delete"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: delete_cv_template called with args:", args);

				const { id } = z
					.object({
						id: z.number().int().positive(),
					})
					.parse(args);

				await cvStructureService.deleteTemplate(id);

				const result = {
					success: true,
					message: "CV template deleted successfully",
				};

				console.log("MCP Tool: delete_cv_template result:", `Deleted template with ID: ${id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: delete_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Set Default CV Template Tool
	server.tool(
		"set_default_cv_template",
		"Set a template as the default CV template (unsets any existing default)",
		{
			id: z.number().int().positive().describe("Template ID to set as default"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: set_default_cv_template called with args:", args);

				const validatedArgs = SetDefaultTemplateSchema.parse(args);
				const template = await cvStructureService.setDefaultTemplate(validatedArgs.id);

				const result = {
					success: true,
					message: "Default CV template set successfully",
					template,
				};

				console.log("MCP Tool: set_default_cv_template result:", `Set default template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: set_default_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Default CV Template Tool
	server.tool("get_default_cv_template", "Retrieve the current default CV template", {}, async (args) => {
		try {
			console.log("MCP Tool: get_default_cv_template called");

			const template = await cvStructureService.getDefaultTemplate();

			if (!template) {
				const result = {
					success: false,
					message: "No default template found",
				};

				console.log("MCP Tool: get_default_cv_template result:", result);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			}

			const result = {
				success: true,
				template,
			};

			console.log("MCP Tool: get_default_cv_template result:", `Found default template: ${template.name}`);

			return {
				content: [
					{
						type: "text",
						text: JSON.stringify(result, null, 2),
					},
				],
			};
		} catch (error) {
			console.error("MCP Tool: get_default_cv_template error:", error);

			return {
				content: [
					{
						type: "text",
						text: JSON.stringify(
							{
								success: false,
								error: error instanceof Error ? error.message : "Unknown error",
							},
							null,
							2,
						),
					},
				],
				isError: true,
			};
		}
	});

	// Get Active CV Templates Tool
	server.tool(
		"get_active_cv_templates",
		"Retrieve all active CV templates ordered by default status then name",
		{},
		async (args) => {
			try {
				console.log("MCP Tool: get_active_cv_templates called");

				const templates = await cvStructureService.getActiveTemplates();

				const result = {
					success: true,
					count: templates.length,
					templates,
				};

				console.log("MCP Tool: get_active_cv_templates result:", `Found ${templates.length} active templates`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_active_cv_templates error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Toggle CV Template Status Tool
	server.tool(
		"toggle_cv_template_status",
		"Toggle the active/inactive status of a CV template",
		{
			id: z.number().int().positive().describe("Template ID to toggle status for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: toggle_cv_template_status called with args:", args);

				const validatedArgs = ToggleTemplateStatusSchema.parse(args);
				const template = await cvStructureService.toggleTemplateStatus(validatedArgs.id);

				const result = {
					success: true,
					message: `Template status toggled to ${template.is_active ? "active" : "inactive"}`,
					template,
				};

				console.log("MCP Tool: toggle_cv_template_status result:", `Toggled status for template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: toggle_cv_template_status error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Duplicate CV Template Tool
	server.tool(
		"duplicate_cv_template",
		"Create a duplicate copy of existing CV template with optional new name",
		{
			id: z.number().int().positive().describe("Template ID to duplicate"),
			new_name: z
				.string()
				.min(1)
				.max(100)
				.optional()
				.describe("Name for the duplicated template (defaults to 'Original Name (Copy)')"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: duplicate_cv_template called with args:", args);

				const validatedArgs = DuplicateTemplateSchema.parse(args);
				const template = await cvStructureService.duplicateTemplate(validatedArgs.id, validatedArgs.new_name);

				const result = {
					success: true,
					message: "CV template duplicated successfully",
					template,
				};

				console.log("MCP Tool: duplicate_cv_template result:", `Duplicated template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: duplicate_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update CV Template Data Tool
	server.tool(
		"update_cv_template_data",
		"Update only the template structure data of a CV template (merge with existing)",
		{
			id: z.number().int().positive().describe("Template ID to update"),
			template: z.record(z.any()).describe("Template structure updates to merge with existing template"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_cv_template_data called with args:", args);

				const validatedArgs = UpdateTemplateDataSchema.parse(args);
				const template = await cvStructureService.updateTemplateData(validatedArgs.id, validatedArgs.template);

				const result = {
					success: true,
					message: "CV template structure updated successfully",
					template,
				};

				console.log("MCP Tool: update_cv_template_data result:", `Updated template structure: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_cv_template_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Restore CV Template Tool
	server.tool(
		"restore_cv_template",
		"Restore a deleted/inactive CV template (sets is_active to true)",
		{
			id: z.number().int().positive().describe("Template ID to restore"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: restore_cv_template called with args:", args);

				const { id } = z
					.object({
						id: z.number().int().positive(),
					})
					.parse(args);

				const template = await cvStructureService.restoreTemplate(id);

				const result = {
					success: true,
					message: "CV template restored successfully",
					template,
				};

				console.log("MCP Tool: restore_cv_template result:", `Restored template: ${template.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: restore_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Clear Default CV Template Tool
	server.tool(
		"clear_default_cv_template",
		"Remove default status from all CV templates (no template will be default)",
		{},
		async (args) => {
			try {
				console.log("MCP Tool: clear_default_cv_template called");

				await cvStructureService.clearDefaultTemplate();

				const result = {
					success: true,
					message: "Default template status cleared from all templates",
				};

				console.log("MCP Tool: clear_default_cv_template result:", result);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: clear_default_cv_template error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	return server;
};

import { PositionService } from "@/services/jobPosition/jobPositionService";
import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { z } from "zod";
import { currency, employmenttype, experiencelevel, positionstatus } from "../../../generated/prisma";

// Initialize position service
const positionService = new PositionService();

// Schema definitions for validation
const CreatePositionSchema = z.object({
	title: z.string().min(1),
	department_id: z.number().int().positive().optional(),
	hiring_manager_id: z.string().optional(),
	job_description: z.string().min(1),
	requirements: z.string().optional(),
	nice_to_have: z.string().optional(),
	responsibilities: z.string().optional(),
	employment_type: z.nativeEnum(employmenttype).optional(),
	experience_level: z.nativeEnum(experiencelevel).optional(),
	salary_min: z.number().positive().optional(),
	salary_max: z.number().positive().optional(),
	currency: z.nativeEnum(currency).optional().default(currency.EUR),
	location: z.string().optional(),
	remote_allowed: z.boolean().optional(),
	status: z.nativeEnum(positionstatus).optional().default(positionstatus.DRAFT),
	posted_date: z.date().optional(),
	closing_date: z.date().optional(),
	positions_available: z.number().int().positive().optional().default(1),
	created_by_id: z.string(),
});

const UpdatePositionSchema = z.object({
	position_id: z.number().int().positive(),
	title: z.string().min(1).optional(),
	department_id: z.number().int().positive().optional(),
	hiring_manager_id: z.string().optional(),
	job_description: z.string().min(1).optional(),
	requirements: z.string().optional(),
	nice_to_have: z.string().optional(),
	responsibilities: z.string().optional(),
	employment_type: z.nativeEnum(employmenttype).optional(),
	experience_level: z.nativeEnum(experiencelevel).optional(),
	salary_min: z.number().positive().optional(),
	salary_max: z.number().positive().optional(),
	currency: z.nativeEnum(currency).optional(),
	location: z.string().optional(),
	remote_allowed: z.boolean().optional(),
	status: z.nativeEnum(positionstatus).optional(),
	posted_date: z.date().optional(),
	closing_date: z.date().optional(),
	positions_available: z.number().int().positive().optional(),
});

const GetPositionSchema = z.object({
	position_id: z.number().int().positive(),
});

const ListPositionsSchema = z.object({
	department_id: z.number().int().positive().optional(),
	hiring_manager_id: z.string().optional(),
	employment_type: z.nativeEnum(employmenttype).optional(),
	experience_level: z.nativeEnum(experiencelevel).optional(),
	status: z.nativeEnum(positionstatus).optional(),
	currency: z.nativeEnum(currency).optional(),
	remote_allowed: z.boolean().optional(),
	salary_min: z.number().positive().optional(),
	salary_max: z.number().positive().optional(),
	location: z.string().optional(),
	search: z.string().optional(),
	posted_after: z.date().optional(),
	posted_before: z.date().optional(),
	closing_after: z.date().optional(),
	closing_before: z.date().optional(),
	limit: z.number().min(1).max(100).optional().default(50),
	offset: z.number().min(0).optional().default(0),
});

const ChangePositionStatusSchema = z.object({
	position_id: z.number().int().positive(),
	status: z.nativeEnum(positionstatus),
});

const DuplicatePositionSchema = z.object({
	position_id: z.number().int().positive(),
	created_by_id: z.string(),
});

const GetPositionsByDepartmentSchema = z.object({
	department_id: z.number().int().positive(),
});

const GetPositionsByHiringManagerSchema = z.object({
	hiring_manager_id: z.string(),
});

export const addHRPositionTools = (server: McpServer): McpServer => {
	// Create Position Tool
	server.tool(
		"create_position",
		"Create a new job position with details like title, department, requirements, salary, and more. Pay attention to Job Description, Requirements, Nice to Have, and Responsibilities fields - they should use markdown formatting for rich text and should not repeat each other.",
		{
			title: z.string().min(1).describe("Job position title (required)"),
			department_id: z
				.number()
				.int()
				.positive()
				.optional()
				.describe("Optional. Department ID where position belongs, only send if there is real department_id"),
			hiring_manager_id: z.string().optional().describe("UUID of hiring manager for this position"),
			job_description: z
				.string()
				.min(1)
				.describe("Detailed job description (required). IMPORTANT: Use markdown formatting for rich text"),
			requirements: z
				.string()
				.optional()
				.describe("Required qualifications and skills. IMPORTANT: Use markdown formatting for rich text"),
			nice_to_have: z
				.string()
				.optional()
				.describe("Preferred qualifications. IMPORTANT: Use markdown formatting for rich text"),
			responsibilities: z
				.string()
				.optional()
				.describe("Key responsibilities of the role. IMPORTANT: Use markdown formatting for rich text"),
			employment_type: z
				.nativeEnum(employmenttype)
				.optional()
				.describe("Employment type (FULL_TIME, PART_TIME, CONTRACT, INTERN)"),
			experience_level: z
				.nativeEnum(experiencelevel)
				.optional()
				.describe("Required experience level (ENTRY, MID, SENIOR, LEAD)"),
			salary_min: z.number().positive().optional().describe("Minimum salary for the position"),
			salary_max: z.number().positive().optional().describe("Maximum salary for the position"),
			currency: z
				.nativeEnum(currency)
				.optional()
				.default(currency.EUR)
				.describe("Salary currency (EUR, USD, GBP, etc.)"),
			location: z.string().optional().describe("Job location"),
			remote_allowed: z.boolean().optional().describe("Whether remote work is allowed"),
			status: z
				.nativeEnum(positionstatus)
				.optional()
				.default(positionstatus.DRAFT)
				.describe("Position status (DRAFT, OPEN, PAUSED, CLOSED, CANCELLED)"),
			posted_date: z.date().optional().describe("Date when position was posted"),
			closing_date: z.date().optional().describe("Application closing date"),
			positions_available: z.number().int().positive().optional().default(1).describe("Number of positions available"),
			created_by_id: z.string().describe("UUID of user creating the position (required)"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: create_position called with args:", args);

				const validatedArgs = CreatePositionSchema.parse(args);
				const position = await positionService.createPosition(validatedArgs);

				const result = {
					success: true,
					message: "Position created successfully",
					position,
				};

				console.log("MCP Tool: create_position result:", `Created position with ID: ${position.id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: create_position error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Position Tool
	server.tool(
		"get_position",
		"Retrieve detailed information about a specific job position including applications",
		{
			position_id: z.number().int().positive().describe("Position ID to fetch"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_position called with args:", args);

				const validatedArgs = GetPositionSchema.parse(args);
				const position = await positionService.getPositionById(validatedArgs.position_id);

				if (!position) {
					const result = {
						success: false,
						message: "Position not found",
					};

					console.log("MCP Tool: get_position result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					position,
				};

				console.log("MCP Tool: get_position result:", `Found position: ${position.title}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_position error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update Position Tool
	server.tool(
		"update_position",
		"Update existing job position information",
		{
			position_id: z.number().int().positive().describe("Position ID to update (required)"),
			title: z.string().min(1).optional().describe("Updated job title"),
			department_id: z.number().int().positive().optional().describe("Updated department ID"),
			hiring_manager_id: z.string().optional().describe("Updated hiring manager UUID"),
			job_description: z.string().min(1).optional().describe("Updated job description"),
			requirements: z.string().optional().describe("Updated requirements"),
			nice_to_have: z.string().optional().describe("Updated nice-to-have qualifications"),
			responsibilities: z.string().optional().describe("Updated responsibilities"),
			employment_type: z.nativeEnum(employmenttype).optional().describe("Updated employment type"),
			experience_level: z.nativeEnum(experiencelevel).optional().describe("Updated experience level"),
			salary_min: z.number().positive().optional().describe("Updated minimum salary"),
			salary_max: z.number().positive().optional().describe("Updated maximum salary"),
			currency: z.nativeEnum(currency).optional().describe("Updated currency"),
			location: z.string().optional().describe("Updated location"),
			remote_allowed: z.boolean().optional().describe("Updated remote work policy"),
			status: z.nativeEnum(positionstatus).optional().describe("Updated status"),
			posted_date: z.date().optional().describe("Updated posted date"),
			closing_date: z.date().optional().describe("Updated closing date"),
			positions_available: z.number().int().positive().optional().describe("Updated number of positions"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_position called with args:", args);

				const validatedArgs = UpdatePositionSchema.parse(args);
				const { position_id, ...updates } = validatedArgs;
				const position = await positionService.updatePosition(position_id, updates);

				const result = {
					success: true,
					message: "Position updated successfully",
					position,
				};

				console.log("MCP Tool: update_position result:", `Updated position: ${position.title}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_position error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// List Positions Tool
	server.tool(
		"list_positions",
		"List job positions with optional filtering by department, status, salary range, and more",
		{
			department_id: z.number().int().positive().optional().describe("Filter by department ID"),
			hiring_manager_id: z.string().optional().describe("Filter by hiring manager id"),
			employment_type: z.nativeEnum(employmenttype).optional().describe("Filter by employment type"),
			experience_level: z.nativeEnum(experiencelevel).optional().describe("Filter by experience level"),
			status: z.nativeEnum(positionstatus).optional().describe("Filter by position status"),
			currency: z.nativeEnum(currency).optional().describe("Filter by salary currency"),
			remote_allowed: z.boolean().optional().describe("Filter by remote work availability"),
			salary_min: z.number().positive().optional().describe("Minimum salary filter"),
			salary_max: z.number().positive().optional().describe("Maximum salary filter"),
			location: z.string().optional().describe("Filter by location (partial match)"),
			search: z.string().optional().describe("Search in title and job description"),
			posted_after: z.date().optional().describe("Filter positions posted after this date"),
			posted_before: z.date().optional().describe("Filter positions posted before this date"),
			closing_after: z.date().optional().describe("Filter positions closing after this date"),
			closing_before: z.date().optional().describe("Filter positions closing before this date"),
			limit: z.number().min(1).max(100).optional().default(50).describe("Limit number of results (1-100)"),
			offset: z.number().min(0).optional().default(0).describe("Offset for pagination"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: list_positions called with args:", args);

				const validatedArgs = ListPositionsSchema.parse(args);
				const { positions, total } = await positionService.listPositions(validatedArgs);

				const result = {
					success: true,
					total,
					count: positions.length,
					positions,
					pagination: {
						limit: validatedArgs.limit,
						offset: validatedArgs.offset,
						hasMore: validatedArgs.offset + positions.length < total,
					},
				};

				console.log("MCP Tool: list_positions result:", `Found ${positions.length} positions out of ${total} total`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: list_positions error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Delete Position Tool
	server.tool(
		"delete_position",
		"Delete a job position permanently (only if no applications exist)",
		{
			position_id: z.number().int().positive().describe("Position ID to delete"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: delete_position called with args:", args);

				const { position_id } = z
					.object({
						position_id: z.number().int().positive(),
					})
					.parse(args);

				await positionService.deletePosition(position_id);

				const result = {
					success: true,
					message: "Position deleted successfully",
				};

				console.log("MCP Tool: delete_position result:", `Deleted position with ID: ${position_id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: delete_position error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Change Position Status Tool
	server.tool(
		"change_position_status",
		"Change the status of a job position (DRAFT, OPEN, PAUSED, CLOSED, CANCELLED)",
		{
			position_id: z.number().int().positive().describe("Position ID to update"),
			status: z.nativeEnum(positionstatus).describe("New status for the position"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: change_position_status called with args:", args);

				const validatedArgs = ChangePositionStatusSchema.parse(args);
				const position = await positionService.changePositionStatus(validatedArgs.position_id, validatedArgs.status);

				const result = {
					success: true,
					message: "Position status updated successfully",
					position,
				};

				console.log("MCP Tool: change_position_status result:", `Updated status for position: ${position.title}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: change_position_status error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Open Positions Tool
	server.tool(
		"get_open_positions",
		"Retrieve all currently open job positions that are accepting applications",
		{},
		async () => {
			try {
				console.log("MCP Tool: get_open_positions called");

				const positions = await positionService.getOpenPositions();

				const result = {
					success: true,
					count: positions.length,
					positions,
				};

				console.log("MCP Tool: get_open_positions result:", `Found ${positions.length} open positions`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_open_positions error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Duplicate Position Tool
	server.tool(
		"duplicate_position",
		"Create a copy of an existing position with DRAFT status",
		{
			position_id: z.number().int().positive().describe("Position ID to duplicate"),
			created_by_id: z.string().describe("UUID of user creating the duplicate"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: duplicate_position called with args:", args);

				const validatedArgs = DuplicatePositionSchema.parse(args);
				const position = await positionService.duplicatePosition(
					validatedArgs.position_id,
					validatedArgs.created_by_id,
				);

				const result = {
					success: true,
					message: "Position duplicated successfully",
					position,
				};

				console.log("MCP Tool: duplicate_position result:", `Duplicated position: ${position.title}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: duplicate_position error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Positions by Department Tool
	server.tool(
		"get_positions_by_department",
		"Retrieve all positions within a specific department",
		{
			department_id: z.number().int().positive().describe("Department ID to fetch positions for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_positions_by_department called with args:", args);

				const validatedArgs = GetPositionsByDepartmentSchema.parse(args);
				const positions = await positionService.getPositionsByDepartment(validatedArgs.department_id);

				const result = {
					success: true,
					count: positions.length,
					positions,
				};

				console.log(
					"MCP Tool: get_positions_by_department result:",
					`Found ${positions.length} positions in department`,
				);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_positions_by_department error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Positions by Hiring Manager Tool
	server.tool(
		"get_positions_by_hiring_manager",
		"Retrieve all positions managed by a specific hiring manager",
		{
			hiring_manager_id: z.string().describe("Hiring manager UUID to fetch positions for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_positions_by_hiring_manager called with args:", args);

				const validatedArgs = GetPositionsByHiringManagerSchema.parse(args);
				const positions = await positionService.getPositionsByHiringManager(validatedArgs.hiring_manager_id);

				const result = {
					success: true,
					count: positions.length,
					positions,
				};

				console.log(
					"MCP Tool: get_positions_by_hiring_manager result:",
					`Found ${positions.length} positions for hiring manager`,
				);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_positions_by_hiring_manager error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	return server;
};

import { env } from "@/common/utils/envConfig";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { addCvManagementTools } from "./tools/cvManagementTools";
import { addCvTemplateManagementTools } from "./tools/cvTemplateManagementTools";
import { addHRPositionTools } from "./tools/hrManagementTools";
import { addUserManagementTools } from "./tools/userManagementTools";

export function createMcpServer(): McpServer {
	// Create MCP server
	const server = new McpServer({
		name: env.MCP_SERVER_NAME || "GenTekAI DB MCP Server",
		description: env.MCP_SERVER_DESCRIPTION || "A Model Context Protocol server for GenTekAI DB",
		version: env.MCP_SERVER_VERSION || "1.0.0",
	});

	addUserManagementTools(server);
	addHRPositionTools(server);
	addCvManagementTools(server);
	addCvTemplateManagementTools(server);
	// Add additional tools as needed
	console.log("MCP Server created with user management tools");

	return server;
}

import dotenv from "dotenv";
import { z } from "zod";

dotenv.config();

const envSchema = z.object({
	NODE_ENV: z.enum(["development", "production", "test"]).default("production"),

	HOST: z.string().min(1).default("localhost"),

	PORT: z.coerce.number().int().positive().default(3002),

	CORS_ORIGIN: z.string().url().default("http://localhost:3002"),

	COMMON_RATE_LIMIT_MAX_REQUESTS: z.coerce.number().int().positive().default(1000),

	COMMON_RATE_LIMIT_WINDOW_MS: z.coerce.number().int().positive().default(1000),
});

const parsedEnv = envSchema.safeParse(process.env);

if (!parsedEnv.success) {
	console.error("❌ Invalid environment variables:", parsedEnv.error.format());
	throw new Error("Invalid environment variables");
}

export const env = {
	...parsedEnv.data,
	isDevelopment: parsedEnv.data.NODE_ENV === "development",
	isProduction: parsedEnv.data.NODE_ENV === "production",
	isTest: parsedEnv.data.NODE_ENV === "test",
	MCP_SERVER_NAME: process.env.MCP_SERVER_NAME || "GenTekAI DB MCP Server",
	MCP_SERVER_VERSION: process.env.MCP_SERVER_VERSION || "1.0.0",
	MCP_SERVER_DESCRIPTION: process.env.MCP_SERVER_DESCRIPTION || "A Model Context Protocol server for GenTekAI DB",
};

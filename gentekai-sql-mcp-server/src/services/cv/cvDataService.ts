import { prisma } from "@/libs/prisma";
import type { Prisma, cv_data } from "../../../generated/prisma";

export interface CreateCvDataInput {
	user_id: string;
	name: string;
	data: object;
	is_public?: boolean;
	source?: string;
}

export interface UpdateCvDataInput {
	name?: string;
	data?: object;
	is_public?: boolean;
	source?: string;
}

export interface ListCvDataFilters {
	user_id?: string;
	is_public?: boolean;
	source?: string;
	search?: string; // Search in name field
	limit?: number;
	offset?: number;
}

export class CvDataService {
	async createCvData(cvData: CreateCvDataInput): Promise<cv_data> {
		const { user_id, name, data, is_public = false, source } = cvData;

		// Verify user exists
		const user = await prisma.users.findUnique({
			where: { id: user_id },
		});

		if (!user) {
			throw new Error("User not found");
		}

		// Check if CV name already exists for this user
		const existingCv = await prisma.cv_data.findFirst({
			where: {
				user_id,
				name,
			},
		});

		if (existingCv) {
			throw new Error("CV with this name already exists for this user");
		}

		const cv = await prisma.cv_data.create({
			data: {
				user_id,
				name,
				data,
				is_public,
				source,
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		return cv;
	}

	async getCvDataById(id: number): Promise<cv_data | null> {
		return await prisma.cv_data.findUnique({
			where: { id },
			include: {
				users: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});
	}

	async getCvDataByUser(user_id: string): Promise<cv_data[]> {
		// Verify user exists
		const user = await prisma.users.findUnique({
			where: { id: user_id },
		});

		if (!user) {
			throw new Error("User not found");
		}

		return await prisma.cv_data.findMany({
			where: { user_id },
			orderBy: { updated_at: "desc" },
		});
	}

	async updateCvData(id: number, updates: UpdateCvDataInput): Promise<cv_data> {
		const existingCv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!existingCv) {
			throw new Error("CV not found");
		}

		// Check for name conflicts if name is being updated
		if (updates.name && updates.name !== existingCv.name) {
			const nameExists = await prisma.cv_data.findFirst({
				where: {
					user_id: existingCv.user_id,
					name: updates.name,
					id: { not: id },
				},
			});

			if (nameExists) {
				throw new Error("CV with this name already exists for this user");
			}
		}

		// Handle data updates (merge with existing data if partial update)
		let updatedData = updates.data;
		if (updates.data && existingCv.data) {
			updatedData = {
				...(existingCv.data as object),
				...updates.data,
			};
		}

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				...updates,
				data: updatedData,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	async deleteCvData(id: number): Promise<void> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		await prisma.cv_data.delete({
			where: { id },
		});
	}

	async listCvData(filters: ListCvDataFilters = {}): Promise<{ cvs: cv_data[]; total: number }> {
		const { user_id, is_public, source, search, limit = 50, offset = 0 } = filters;

		// Build where conditions
		const whereConditions: Prisma.cv_dataWhereInput = {};

		if (user_id) {
			whereConditions.user_id = user_id;
		}

		if (is_public !== undefined) {
			whereConditions.is_public = is_public;
		}

		if (source) {
			whereConditions.source = source;
		}

		if (search) {
			whereConditions.name = {
				contains: search,
				mode: "insensitive",
			};
		}

		// Execute both queries in parallel
		const [cvs, total] = await Promise.all([
			prisma.cv_data.findMany({
				where: whereConditions,
				include: {
					users: {
						select: {
							id: true,
							email: true,
							first_name: true,
							last_name: true,
						},
					},
				},
				orderBy: { updated_at: "desc" },
				take: limit,
				skip: offset,
			}),
			prisma.cv_data.count({
				where: whereConditions,
			}),
		]);

		return { cvs, total };
	}

	async updateCvDataData(id: number, dataUpdates: object): Promise<cv_data> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		const updatedData = {
			...((cv.data as object) || {}),
			...dataUpdates,
		};

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				data: updatedData,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	async duplicateCvData(id: number, newName?: string): Promise<cv_data> {
		const originalCv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!originalCv) {
			throw new Error("CV not found");
		}

		const duplicateName = newName || `${originalCv.name} (Copy)`;

		// Check if duplicate name already exists
		const nameExists = await prisma.cv_data.findFirst({
			where: {
				user_id: originalCv.user_id,
				name: duplicateName,
			},
		});

		if (nameExists) {
			throw new Error("CV with this name already exists for this user");
		}

		const duplicatedCv = await prisma.cv_data.create({
			data: {
				user_id: originalCv.user_id,
				name: duplicateName,
				data: originalCv.data as object,
				is_public: false, // Set to private by default for duplicates
				source: originalCv.source,
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		return duplicatedCv;
	}

	async toggleCvVisibility(id: number): Promise<cv_data> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				is_public: !cv.is_public,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	async getPublicCvData(): Promise<cv_data[]> {
		return await prisma.cv_data.findMany({
			where: { is_public: true },
			include: {
				users: {
					select: {
						id: true,
						first_name: true,
						last_name: true,
						// Don't include email for public CVs for privacy
					},
				},
			},
			orderBy: { updated_at: "desc" },
		});
	}

	async getCvDataBySource(source: string): Promise<cv_data[]> {
		return await prisma.cv_data.findMany({
			where: { source },
			include: {
				users: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
			orderBy: { updated_at: "desc" },
		});
	}
}

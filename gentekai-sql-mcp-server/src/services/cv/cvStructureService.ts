import { prisma } from "@/libs/prisma";
import type { Prisma, cv_structure_templates } from "../../../generated/prisma";

export interface CreateTemplateInput {
	name: string;
	description?: string;
	template: object;
	is_active?: boolean;
	is_default?: boolean;
}

export interface UpdateTemplateInput {
	name?: string;
	description?: string;
	template?: object;
	is_active?: boolean;
	is_default?: boolean;
}

export interface ListTemplatesFilters {
	is_active?: boolean;
	is_default?: boolean;
	search?: string; // Search in name and description
	limit?: number;
	offset?: number;
}

export class CvStructureService {
	async createTemplate(templateData: CreateTemplateInput): Promise<cv_structure_templates> {
		const { name, description, template, is_active = true, is_default = false } = templateData;

		// Check if template name already exists
		const existingTemplate = await prisma.cv_structure_templates.findFirst({
			where: {
				name,
				is_active: true,
			},
		});

		if (existingTemplate) {
			throw new Error("Template with this name already exists");
		}

		// If this is set as default, unset any existing default
		if (is_default) {
			await prisma.cv_structure_templates.updateMany({
				where: { is_default: true },
				data: {
					is_default: false,
					updated_at: new Date(),
				},
			});
		}

		const newTemplate = await prisma.cv_structure_templates.create({
			data: {
				name,
				description,
				template,
				is_active,
				is_default,
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		return newTemplate;
	}

	async getTemplateById(id: number): Promise<cv_structure_templates | null> {
		return await prisma.cv_structure_templates.findUnique({
			where: { id },
		});
	}

	async getTemplateByName(name: string): Promise<cv_structure_templates | null> {
		return await prisma.cv_structure_templates.findFirst({
			where: {
				name,
				is_active: true,
			},
		});
	}

	async updateTemplate(id: number, updates: UpdateTemplateInput): Promise<cv_structure_templates> {
		const existingTemplate = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!existingTemplate) {
			throw new Error("Template not found");
		}

		// Check for name conflicts if name is being updated
		if (updates.name && updates.name !== existingTemplate.name) {
			const nameExists = await prisma.cv_structure_templates.findFirst({
				where: {
					name: updates.name,
					is_active: true,
					id: { not: id },
				},
			});

			if (nameExists) {
				throw new Error("Template with this name already exists");
			}
		}

		// If setting this as default, unset any existing default
		if (updates.is_default === true) {
			await prisma.cv_structure_templates.updateMany({
				where: {
					is_default: true,
					id: { not: id },
				},
				data: {
					is_default: false,
					updated_at: new Date(),
				},
			});
		}

		// Handle template updates (merge with existing template if partial update)
		let updatedTemplate = updates.template;
		if (updates.template && existingTemplate.template) {
			updatedTemplate = {
				...(existingTemplate.template as object),
				...updates.template,
			};
		}

		const updated = await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				...updates,
				template: updatedTemplate,
				updated_at: new Date(),
			},
		});

		return updated;
	}

	async deleteTemplate(id: number): Promise<void> {
		const template = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!template) {
			throw new Error("Template not found");
		}

		// Soft delete by setting is_active to false
		await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				is_active: false,
				is_default: false, // Remove default status when deleting
				updated_at: new Date(),
			},
		});
	}

	async listTemplates(
		filters: ListTemplatesFilters = {},
	): Promise<{ templates: cv_structure_templates[]; total: number }> {
		const { is_active, is_default, search, limit = 50, offset = 0 } = filters;

		// Build where conditions
		const whereConditions: Prisma.cv_structure_templatesWhereInput = {};

		if (is_active !== undefined) {
			whereConditions.is_active = is_active;
		}

		if (is_default !== undefined) {
			whereConditions.is_default = is_default;
		}

		if (search) {
			whereConditions.OR = [
				{ name: { contains: search, mode: "insensitive" } },
				{ description: { contains: search, mode: "insensitive" } },
			];
		}

		// Execute both queries in parallel
		const [templates, total] = await Promise.all([
			prisma.cv_structure_templates.findMany({
				where: whereConditions,
				orderBy: [
					{ is_default: "desc" }, // Default templates first
					{ created_at: "desc" },
				],
				take: limit,
				skip: offset,
			}),
			prisma.cv_structure_templates.count({
				where: whereConditions,
			}),
		]);

		return { templates, total };
	}

	async setDefaultTemplate(id: number): Promise<cv_structure_templates> {
		const template = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!template) {
			throw new Error("Template not found");
		}

		if (!template.is_active) {
			throw new Error("Cannot set inactive template as default");
		}

		// Unset any existing default templates
		await prisma.cv_structure_templates.updateMany({
			where: { is_default: true },
			data: {
				is_default: false,
				updated_at: new Date(),
			},
		});

		// Set this template as default
		const updatedTemplate = await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				is_default: true,
				updated_at: new Date(),
			},
		});

		return updatedTemplate;
	}

	async getDefaultTemplate(): Promise<cv_structure_templates | null> {
		return await prisma.cv_structure_templates.findFirst({
			where: {
				is_default: true,
				is_active: true,
			},
		});
	}

	async getActiveTemplates(): Promise<cv_structure_templates[]> {
		return await prisma.cv_structure_templates.findMany({
			where: { is_active: true },
			orderBy: [{ is_default: "desc" }, { name: "asc" }],
		});
	}

	async toggleTemplateStatus(id: number): Promise<cv_structure_templates> {
		const template = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!template) {
			throw new Error("Template not found");
		}

		// If deactivating a default template, unset default status
		const updates: Partial<UpdateTemplateInput> = {
			is_active: !template.is_active,
		};

		if (template.is_default && template.is_active) {
			updates.is_default = false;
		}

		const updatedTemplate = await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				...updates,
				updated_at: new Date(),
			},
		});

		return updatedTemplate;
	}

	async duplicateTemplate(id: number, newName?: string): Promise<cv_structure_templates> {
		const originalTemplate = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!originalTemplate) {
			throw new Error("Template not found");
		}

		const duplicateName = newName || `${originalTemplate.name} (Copy)`;

		// Check if duplicate name already exists
		const nameExists = await prisma.cv_structure_templates.findFirst({
			where: {
				name: duplicateName,
				is_active: true,
			},
		});

		if (nameExists) {
			throw new Error("Template with this name already exists");
		}

		const duplicatedTemplate = await prisma.cv_structure_templates.create({
			data: {
				name: duplicateName,
				description: originalTemplate.description,
				template: originalTemplate.template as object,
				is_active: true,
				is_default: false, // Duplicates are never default
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		return duplicatedTemplate;
	}

	async updateTemplateData(id: number, templateUpdates: object): Promise<cv_structure_templates> {
		const template = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!template) {
			throw new Error("Template not found");
		}

		const updatedTemplate = {
			...((template.template as object) || {}),
			...templateUpdates,
		};

		const updated = await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				template: updatedTemplate,
				updated_at: new Date(),
			},
		});

		return updated;
	}

	async restoreTemplate(id: number): Promise<cv_structure_templates> {
		const template = await prisma.cv_structure_templates.findUnique({
			where: { id },
		});

		if (!template) {
			throw new Error("Template not found");
		}

		const restoredTemplate = await prisma.cv_structure_templates.update({
			where: { id },
			data: {
				is_active: true,
				updated_at: new Date(),
			},
		});

		return restoredTemplate;
	}

	async getTemplatesByStatus(is_active: boolean): Promise<cv_structure_templates[]> {
		return await prisma.cv_structure_templates.findMany({
			where: { is_active },
			orderBy: [{ is_default: "desc" }, { updated_at: "desc" }],
		});
	}

	async clearDefaultTemplate(): Promise<void> {
		await prisma.cv_structure_templates.updateMany({
			where: { is_default: true },
			data: {
				is_default: false,
				updated_at: new Date(),
			},
		});
	}
}

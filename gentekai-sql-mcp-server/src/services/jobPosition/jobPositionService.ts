import { prisma } from "@/libs/prisma";
import {
	Prisma,
	type currency,
	type employmenttype,
	type experiencelevel,
	type positions,
	positionstatus,
} from "../../../generated/prisma";

export interface CreatePositionInput {
	title: string;
	department_id?: number;
	hiring_manager_id?: string;
	job_description: string;
	requirements?: string;
	nice_to_have?: string;
	responsibilities?: string;
	employment_type?: employmenttype;
	experience_level?: experiencelevel;
	salary_min?: number;
	salary_max?: number;
	currency?: currency;
	location?: string;
	remote_allowed?: boolean;
	status?: positionstatus;
	posted_date?: Date;
	closing_date?: Date;
	positions_available?: number;
	created_by_id: string;
}

export interface UpdatePositionInput {
	title?: string;
	department_id?: number;
	hiring_manager_id?: string;
	job_description?: string;
	requirements?: string;
	nice_to_have?: string;
	responsibilities?: string;
	employment_type?: employmenttype;
	experience_level?: experiencelevel;
	salary_min?: number;
	salary_max?: number;
	currency?: currency;
	location?: string;
	remote_allowed?: boolean;
	status?: positionstatus;
	posted_date?: Date;
	closing_date?: Date;
	positions_available?: number;
}

export interface ListPositionsFilters {
	department_id?: number;
	hiring_manager_id?: string;
	employment_type?: employmenttype;
	experience_level?: experiencelevel;
	status?: positionstatus;
	currency?: currency;
	remote_allowed?: boolean;
	salary_min?: number;
	salary_max?: number;
	location?: string;
	search?: string; // Search in title, job_description
	posted_after?: Date;
	posted_before?: Date;
	closing_after?: Date;
	closing_before?: Date;
	limit?: number;
	offset?: number;
}

export class PositionService {
	async createPosition(positionData: CreatePositionInput): Promise<positions> {
		const {
			title,
			department_id,
			hiring_manager_id,
			job_description,
			requirements,
			nice_to_have,
			responsibilities,
			employment_type,
			experience_level,
			salary_min,
			salary_max,
			currency = "EUR", // Default to EUR if not provided
			location,
			remote_allowed,
			status = positionstatus.DRAFT,
			posted_date,
			closing_date,
			positions_available = 1,
			created_by_id,
		} = positionData;

		// // Validate department exists
		// const department = await prisma.departments.findUnique({
		// 	where: { id: department_id },
		// });

		// if (!department) {
		// 	throw new Error("Department not found");
		// }

		// Validate created_by user exists
		const createdByUser = await prisma.users.findUnique({
			where: { id: created_by_id },
		});

		if (!createdByUser) {
			throw new Error("Created by user not found");
		}

		// Validate hiring manager if provided
		if (hiring_manager_id) {
			const hiringManager = await prisma.users.findUnique({
				where: { id: hiring_manager_id },
			});

			if (!hiringManager) {
				throw new Error("Hiring manager not found");
			}
		}

		// Validate salary range
		if (salary_min && salary_max && salary_min > salary_max) {
			throw new Error("Minimum salary cannot be greater than maximum salary");
		}

		// Validate that if salary is provided, currency should be set
		if ((salary_min || salary_max) && !currency) {
			throw new Error("Currency must be specified when salary range is provided");
		}

		// Validate closing date
		if (closing_date && closing_date <= new Date()) {
			throw new Error("Closing date must be in the future");
		}

		// If status is OPEN and no posted_date, set it to now
		const finalPostedDate = status === positionstatus.OPEN && !posted_date ? new Date() : posted_date;

		const position = await prisma.positions.create({
			data: {
				title,
				department_id,
				hiring_manager_id,
				job_description,
				requirements,
				nice_to_have,
				responsibilities,
				employment_type,
				experience_level,
				salary_min: salary_min ? new Prisma.Decimal(salary_min) : null,
				salary_max: salary_max ? new Prisma.Decimal(salary_max) : null,
				currency,
				location,
				remote_allowed,
				status,
				posted_date: finalPostedDate,
				closing_date,
				positions_available,
				created_by_id,
				created_at: new Date(),
				updated_at: new Date(),
			},
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});

		return position;
	}

	async getPositionById(id: number): Promise<positions | null> {
		return await prisma.positions.findUnique({
			where: { id },
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				applications: {
					include: {
						users_applications_candidate_idTousers: {
							select: {
								id: true,
								email: true,
								first_name: true,
								last_name: true,
							},
						},
					},
				},
			},
		});
	}

	async updatePosition(id: number, updates: UpdatePositionInput): Promise<positions> {
		const existingPosition = await prisma.positions.findUnique({
			where: { id },
		});

		if (!existingPosition) {
			throw new Error("Position not found");
		}

		// Validate department if being updated
		if (updates.department_id) {
			const department = await prisma.departments.findUnique({
				where: { id: updates.department_id },
			});

			if (!department) {
				throw new Error("Department not found");
			}
		}

		// Validate hiring manager if being updated
		if (updates.hiring_manager_id) {
			const hiringManager = await prisma.users.findUnique({
				where: { id: updates.hiring_manager_id },
			});

			if (!hiringManager) {
				throw new Error("Hiring manager not found");
			}
		}

		// Validate salary range
		const newSalaryMin =
			updates.salary_min ?? (existingPosition.salary_min ? existingPosition.salary_min.toNumber() : undefined);
		const newSalaryMax =
			updates.salary_max ?? (existingPosition.salary_max ? existingPosition.salary_max.toNumber() : undefined);

		if (newSalaryMin && newSalaryMax && newSalaryMin > newSalaryMax) {
			throw new Error("Minimum salary cannot be greater than maximum salary");
		}

		// Validate that if salary is being updated, currency should be considered
		const finalCurrency = updates.currency ?? existingPosition.currency;
		if ((updates.salary_min || updates.salary_max || newSalaryMin || newSalaryMax) && !finalCurrency) {
			throw new Error("Currency must be specified when salary range is provided");
		}

		// Validate closing date
		if (updates.closing_date && updates.closing_date <= new Date()) {
			throw new Error("Closing date must be in the future");
		}

		// Handle status changes
		const finalUpdates = { ...updates };
		if (updates.status === positionstatus.OPEN && !existingPosition.posted_date) {
			finalUpdates.posted_date = new Date();
		}

		const updatedPosition = await prisma.positions.update({
			where: { id },
			data: {
				...finalUpdates,
				salary_min: updates.salary_min ? new Prisma.Decimal(updates.salary_min) : undefined,
				salary_max: updates.salary_max ? new Prisma.Decimal(updates.salary_max) : undefined,
				updated_at: new Date(),
			},
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});

		return updatedPosition;
	}

	async deletePosition(id: number): Promise<void> {
		const position = await prisma.positions.findUnique({
			where: { id },
			include: {
				applications: true,
			},
		});

		if (!position) {
			throw new Error("Position not found");
		}

		// Check if position has applications
		if (position.applications.length > 0) {
			throw new Error("Cannot delete position with existing applications. Set status to CANCELLED instead.");
		}

		await prisma.positions.delete({
			where: { id },
		});
	}

	async listPositions(filters: ListPositionsFilters = {}): Promise<{ positions: positions[]; total: number }> {
		const {
			department_id,
			hiring_manager_id,
			employment_type,
			experience_level,
			status,
			currency: filterCurrency,
			remote_allowed,
			salary_min,
			salary_max,
			location,
			search,
			posted_after,
			posted_before,
			closing_after,
			closing_before,
			limit = 50,
			offset = 0,
		} = filters;

		// Build where conditions
		const whereConditions: Prisma.positionsWhereInput = {};

		if (department_id) {
			whereConditions.department_id = department_id;
		}

		if (hiring_manager_id) {
			whereConditions.hiring_manager_id = hiring_manager_id;
		}

		if (employment_type) {
			whereConditions.employment_type = employment_type;
		}

		if (experience_level) {
			whereConditions.experience_level = experience_level;
		}

		if (status) {
			whereConditions.status = status;
		}

		if (filterCurrency) {
			whereConditions.currency = filterCurrency;
		}

		if (remote_allowed !== undefined) {
			whereConditions.remote_allowed = remote_allowed;
		}

		if (location) {
			whereConditions.location = {
				contains: location,
				mode: "insensitive",
			};
		}

		if (salary_min || salary_max) {
			whereConditions.AND = [];

			if (salary_min) {
				whereConditions.AND.push({
					salary_max: {
						gte: new Prisma.Decimal(salary_min),
					},
				});
			}

			if (salary_max) {
				whereConditions.AND.push({
					salary_min: {
						lte: new Prisma.Decimal(salary_max),
					},
				});
			}
		}

		if (search) {
			whereConditions.OR = [
				{ title: { contains: search, mode: "insensitive" } },
				{ job_description: { contains: search, mode: "insensitive" } },
			];
		}

		// Date filters
		if (posted_after || posted_before) {
			whereConditions.posted_date = {};
			if (posted_after) {
				whereConditions.posted_date.gte = posted_after;
			}
			if (posted_before) {
				whereConditions.posted_date.lte = posted_before;
			}
		}

		if (closing_after || closing_before) {
			whereConditions.closing_date = {};
			if (closing_after) {
				whereConditions.closing_date.gte = closing_after;
			}
			if (closing_before) {
				whereConditions.closing_date.lte = closing_before;
			}
		}

		// Execute both queries in parallel
		const [positions, total] = await Promise.all([
			prisma.positions.findMany({
				where: whereConditions,
				include: {
					departments: true,
					users_positions_created_by_idTousers: {
						select: {
							id: true,
							email: true,
							first_name: true,
							last_name: true,
						},
					},
					users_positions_hiring_manager_idTousers: {
						select: {
							id: true,
							email: true,
							first_name: true,
							last_name: true,
						},
					},
					_count: {
						select: {
							applications: true,
						},
					},
				},
				orderBy: { created_at: "desc" },
				take: limit,
				skip: offset,
			}),
			prisma.positions.count({
				where: whereConditions,
			}),
		]);

		return { positions, total };
	}

	async changePositionStatus(id: number, status: positionstatus): Promise<positions> {
		const position = await prisma.positions.findUnique({
			where: { id },
		});

		if (!position) {
			throw new Error("Position not found");
		}

		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		const updateData: any = {
			status,
			updated_at: new Date(),
		};

		// Set posted_date when opening a position
		if (status === positionstatus.OPEN && !position.posted_date) {
			updateData.posted_date = new Date();
		}

		const updatedPosition = await prisma.positions.update({
			where: { id },
			data: updateData,
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});

		return updatedPosition;
	}

	async getPositionsByDepartment(department_id: number): Promise<positions[]> {
		const department = await prisma.departments.findUnique({
			where: { id: department_id },
		});

		if (!department) {
			throw new Error("Department not found");
		}

		return await prisma.positions.findMany({
			where: { department_id },
			include: {
				departments: true,
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				_count: {
					select: {
						applications: true,
					},
				},
			},
			orderBy: { created_at: "desc" },
		});
	}

	async getPositionsByHiringManager(hiring_manager_id: string): Promise<positions[]> {
		const hiringManager = await prisma.users.findUnique({
			where: { id: hiring_manager_id },
		});

		if (!hiringManager) {
			throw new Error("Hiring manager not found");
		}

		return await prisma.positions.findMany({
			where: { hiring_manager_id },
			include: {
				departments: true,
				_count: {
					select: {
						applications: true,
					},
				},
			},
			orderBy: { created_at: "desc" },
		});
	}

	async getOpenPositions(): Promise<positions[]> {
		return await prisma.positions.findMany({
			where: {
				status: positionstatus.OPEN,
				OR: [{ closing_date: null }, { closing_date: { gt: new Date() } }],
			},
			include: {
				departments: true,
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						first_name: true,
						last_name: true,
					},
				},
				_count: {
					select: {
						applications: true,
					},
				},
			},
			orderBy: { posted_date: "desc" },
		});
	}

	async duplicatePosition(id: number, created_by_id: string): Promise<positions> {
		const originalPosition = await prisma.positions.findUnique({
			where: { id },
		});

		if (!originalPosition) {
			throw new Error("Position not found");
		}

		// Validate created_by user exists
		const createdByUser = await prisma.users.findUnique({
			where: { id: created_by_id },
		});

		if (!createdByUser) {
			throw new Error("Created by user not found");
		}

		const duplicatedPosition = await prisma.positions.create({
			data: {
				title: `${originalPosition.title} (Copy)`,
				department_id: originalPosition.department_id,
				hiring_manager_id: originalPosition.hiring_manager_id,
				job_description: originalPosition.job_description,
				requirements: originalPosition.requirements,
				nice_to_have: originalPosition.nice_to_have,
				responsibilities: originalPosition.responsibilities,
				employment_type: originalPosition.employment_type,
				experience_level: originalPosition.experience_level,
				salary_min: originalPosition.salary_min,
				salary_max: originalPosition.salary_max,
				currency: originalPosition.currency,
				location: originalPosition.location,
				remote_allowed: originalPosition.remote_allowed,
				status: positionstatus.DRAFT, // Always start as draft
				posted_date: null,
				closing_date: null,
				positions_available: originalPosition.positions_available,
				created_by_id,
				created_at: new Date(),
				updated_at: new Date(),
			},
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});

		return duplicatedPosition;
	}

	async getPositionsByCurrency(currency: currency): Promise<positions[]> {
		return await prisma.positions.findMany({
			where: { currency },
			include: {
				departments: true,
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				_count: {
					select: {
						applications: true,
					},
				},
			},
			orderBy: { created_at: "desc" },
		});
	}

	async updateSalaryAndCurrency(
		id: number,
		salary_min?: number,
		salary_max?: number,
		currency?: currency,
	): Promise<positions> {
		const position = await prisma.positions.findUnique({
			where: { id },
		});

		if (!position) {
			throw new Error("Position not found");
		}

		// Validate salary range
		if (salary_min && salary_max && salary_min > salary_max) {
			throw new Error("Minimum salary cannot be greater than maximum salary");
		}

		// If setting salary, ensure currency is provided
		if ((salary_min || salary_max) && !currency && !position.currency) {
			throw new Error("Currency must be specified when setting salary range");
		}

		const updatedPosition = await prisma.positions.update({
			where: { id },
			data: {
				salary_min: salary_min ? new Prisma.Decimal(salary_min) : undefined,
				salary_max: salary_max ? new Prisma.Decimal(salary_max) : undefined,
				currency: currency || undefined,
				updated_at: new Date(),
			},
			include: {
				departments: true,
				users_positions_created_by_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
				users_positions_hiring_manager_idTousers: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});

		return updatedPosition;
	}
}

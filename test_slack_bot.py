#!/usr/bin/env python3
"""
Test script for Slack bot integration
"""
import hashlib
import hmac
import json
import time
import requests

# Slack signing secret from your .env.secrets
SLACK_SIGNING_SECRET = "c447cf199c2d12dfc45ba99817d10b5c"
BACKEND_URL = "http://localhost:9876"

def create_slack_signature(body: str, timestamp: str) -> str:
    """Create valid Slack signature"""
    sig_basestring = f"v0:{timestamp}:{body}"
    computed_signature = (
        "v0="
        + hmac.new(
            SLACK_SIGNING_SECRET.encode(), 
            sig_basestring.encode(), 
            hashlib.sha256
        ).hexdigest()
    )
    return computed_signature

def test_url_verification():
    """Test Slack URL verification challenge"""
    print("🔍 Testing Slack URL verification...")
    
    timestamp = str(int(time.time()))
    payload = {
        "token": "test_token",
        "challenge": "test_challenge_12345",
        "type": "url_verification"
    }
    
    body = json.dumps(payload)
    signature = create_slack_signature(body, timestamp)
    
    headers = {
        "Content-Type": "application/json",
        "X-Slack-Request-Timestamp": timestamp,
        "X-Slack-Signature": signature
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/slack/events",
            headers=headers,
            data=body,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code in [200, 202]:
            result = response.json()
            if result.get("challenge") == payload["challenge"]:
                print("✅ URL verification test PASSED!")
                return True
            else:
                print("❌ Challenge response mismatch")
                return False
        else:
            print(f"❌ URL verification test FAILED with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during URL verification test: {e}")
        return False

def test_app_mention():
    """Test Slack app mention event"""
    print("\n🤖 Testing Slack app mention...")
    
    timestamp = str(int(time.time()))
    payload = {
        "token": "test_token",
        "team_id": "T1234567890",
        "api_app_id": "A1234567890",
        "event": {
            "type": "app_mention",
            "channel": "C1234567890",
            "user": "U1234567890",
            "text": "<@A0920AVH9B2> Hello, can you help me?",
            "ts": "1234567890.123456",
            "thread_ts": None
        },
        "type": "event_callback",
        "event_id": "Ev1234567890",
        "event_time": int(time.time())
    }
    
    body = json.dumps(payload)
    signature = create_slack_signature(body, timestamp)
    
    headers = {
        "Content-Type": "application/json",
        "X-Slack-Request-Timestamp": timestamp,
        "X-Slack-Signature": signature
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/slack/events",
            headers=headers,
            data=body,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 202:
            print("✅ App mention test PASSED!")
            return True
        else:
            print(f"❌ App mention test FAILED with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during app mention test: {e}")
        return False

def test_direct_message():
    """Test Slack direct message event"""
    print("\n💬 Testing Slack direct message...")
    
    timestamp = str(int(time.time()))
    payload = {
        "token": "test_token",
        "team_id": "T1234567890",
        "api_app_id": "A1234567890",
        "event": {
            "type": "message",
            "channel": "D1234567890",  # DM channel
            "channel_type": "im",
            "user": "U1234567890",
            "text": "Hello, I need help with something",
            "ts": "1234567890.123456"
        },
        "type": "event_callback",
        "event_id": "Ev1234567891",
        "event_time": int(time.time())
    }
    
    body = json.dumps(payload)
    signature = create_slack_signature(body, timestamp)
    
    headers = {
        "Content-Type": "application/json",
        "X-Slack-Request-Timestamp": timestamp,
        "X-Slack-Signature": signature
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/slack/events",
            headers=headers,
            data=body,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 202:
            print("✅ Direct message test PASSED!")
            return True
        else:
            print(f"❌ Direct message test FAILED with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error during direct message test: {e}")
        return False

def main():
    """Run all Slack bot tests"""
    print("🚀 Starting Slack Bot Integration Tests")
    print("=" * 50)
    
    # Test backend health first
    try:
        health_response = requests.get(f"{BACKEND_URL}/-/health", timeout=5)
        if health_response.status_code != 200:
            print(f"❌ Backend health check failed: {health_response.status_code}")
            return
        print("✅ Backend is healthy")
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    if test_url_verification():
        tests_passed += 1
    
    if test_app_mention():
        tests_passed += 1
        
    if test_direct_message():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All Slack bot tests PASSED!")
    else:
        print("⚠️  Some tests failed. Check the logs above.")

if __name__ == "__main__":
    main()
